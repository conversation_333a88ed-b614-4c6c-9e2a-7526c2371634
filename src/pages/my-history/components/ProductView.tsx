import { i18next } from '@ali/dingtalk-i18n'; import React from 'react';
import { InfiniteList } from '@/components/InfiniteList';
import { useHistoryProductList } from '../services';
import ProductListItem from '@/components/ProductListItem';
import { RecentlyUsedLOutlined } from '@ali/ding-icons';
import './ProductView.less';

const ProductView: React.FC = () => {
  const {
    data,
    loading,
    loadingMore,
    error,
    loadMore,
    noMore,
  } = useHistoryProductList();

  const products = data?.list || [];

  // 渲染单个商品卡片
  const renderProductItem = (product: any) => (<ProductListItem
    key={product.productId}
    product={product}
    showHeart
  />);


  return (
    <InfiniteList
      data={products}
      renderItem={renderProductItem}
      loading={loading || loadingMore}
      error={!!error}
      hasMore={!noMore}
      layout="list"
      onLoadMore={loadMore}
      emptyIcon={<RecentlyUsedLOutlined />}
      emptyText={i18next.t('j-dingtalk-web_pages_my-history_components_ProductView_NoBrowsingHistory')}
      errorText={i18next.t('j-dingtalk-web_pages_my-history_components_ProductView_NetworkErrorPleaseTryAgain')}
      className="product-view-list"
    />);
};

export default ProductView;
