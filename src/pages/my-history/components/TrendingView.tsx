import React from 'react';
import { RecFeedDetail } from '@/apis/feed-action';
import { useBrowseProductList, generateProductUrl } from '../services';
import $openLink from '@ali/dingtalk-jsapi/api/biz/util/openLink';
import { InfiniteList } from '@/components/InfiniteList';
import { RecentlyUsedLOutlined } from '@ali/ding-icons';
import ProductCard from '../../my-like/components/ProductCard';

const TrendingView: React.FC = () => {
  const {
    data,
    loading,
    error,
    loadMore,
    loadingMore,
    noMore,
  } = useBrowseProductList();

  const products = data?.list || [];

  // 点击跳转
  const handleProductClick = (product: RecFeedDetail) => {
    const url = generateProductUrl(product);
    $openLink({ url });
  };

  return (
    <InfiniteList
      data={products}
      renderItem={(item: RecFeedDetail) => (
        <ProductCard key={item.itemId} product={item} onProductClick={handleProductClick} />
      )}
      loading={loading || loadingMore}
      error={!!error}
      hasMore={!noMore}
      layout="waterfall"
      onLoadMore={loadMore}
      emptyIcon={<RecentlyUsedLOutlined />}
    />
  );
};

export default TrendingView;
