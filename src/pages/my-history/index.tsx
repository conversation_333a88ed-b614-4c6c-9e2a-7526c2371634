import React from 'react';
import { i18next } from '@ali/dingtalk-i18n';
import TrendingView from './components/TrendingView';
import ProductView from './components/ProductView';
import { Tabs } from 'dingtalk-design-mobile';
import { useTitle } from '@/hooks/useTitle';
import './index.less';

function MyHistory() {
  useTitle(i18next.t('j-dingtalk-web_pages_my-history_BrowsingHistory'));

  const tabs = [
    { title: i18next.t('j-dingtalk-web_pages_my-history_Video'), key: '1' },
    { title: i18next.t('j-dingtalk-web_pages_my-history_Commodity'), key: '2' }];


  return (
    <div className="my-history-page">
      <Tabs
        className="my-history-tabs"
        tabs={tabs}
        hideBottomDivider
      >

        <div className="my-history-tab-content" key="1">
          <TrendingView />
        </div>
        <div className="my-history-tab-content" key="2">
          <ProductView />
        </div>
      </Tabs>
    </div>
  );
}

export default MyHistory;
