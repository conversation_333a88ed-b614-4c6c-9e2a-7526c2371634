
import { useInfiniteScroll } from 'ahooks';
import {
  queryBrowseHistory,
  RecQueryBrowseHistoryRequest,
  RecQueryBrowseHistoryResponse,
  RecFeedDetail,
} from '@/apis/feed-action';
import {
  queryUserFootprint,
  IJUserProductRequest,
  IJUserProductResponse,
  IProjectModel,
} from '@/apis/user-recommend';
import { TENANT_ID } from '@/common/constants';
import { getSevenDingUserInfo } from '@/utils/jsapi';

// 获取浏览历史商品列表
export const getBrowseProductList = async (params: {
  cursor?: string;
  size?: number;
}): Promise<{
  data: RecFeedDetail[];
  cursor: string;
  hasMore: boolean;
}> => {
  const requestParams: RecQueryBrowseHistoryRequest = {
    tenantId: TENANT_ID, // 使用常量
    cursor: params.cursor || undefined,
    size: params.size || 20,
  };

  const response: RecQueryBrowseHistoryResponse = await queryBrowseHistory(requestParams);

  return {
    data: response.items,
    cursor: response.cursor,
    hasMore: response.hasMore,
  };
};

// 使用 useInfiniteScroll 封装请求，支持无限滚动
export const useBrowseProductList = () => {
  return useInfiniteScroll(
    async (params: { list?: RecFeedDetail[]; cursor?: string; size?: number } = {}) => {
      const result = await getBrowseProductList(params);
      return {
        list: result.data,
        cursor: result.cursor,
        hasMore: result.hasMore,
      };
    },
    {
      isNoMore: (res) => !res?.hasMore,
    },
  );
};

// 生成产品详情链接
export const generateProductUrl = (product: RecFeedDetail): string => {
  // 使用 dingtalk 协议拼接 URL，itemId 作为 feedId
  return `dingtalk://dingtalkclient/page/j_feed_detail?feedId=${product.itemId}`;
};

// 获取历史浏览商品列表
export const getHistoryProductList = async (params: {
  cursor?: string;
  size?: number;
}): Promise<{
  data: IProjectModel[];
  cursor: string;
  hasMore: boolean;
}> => {
  const { token } = await getSevenDingUserInfo();

  const requestParams: IJUserProductRequest = {
    cursor: params.cursor,
    size: params.size || 20,
    hhoToken: token,
  };

  const response: IJUserProductResponse = await queryUserFootprint(requestParams);

  return {
    data: response.products,
    cursor: response.nextCursor,
    hasMore: response.hasMore,
  };
};

// 使用 useInfiniteScroll 封装历史浏览商品请求，支持无限滚动
export const useHistoryProductList = () => {
  return useInfiniteScroll(
    async (params: { list?: IProjectModel[]; cursor?: string; size?: number } = {}) => {
      const result = await getHistoryProductList(params);
      return {
        list: result.data,
        cursor: result.cursor,
        hasMore: result.hasMore,
      };
    },
    {
      isNoMore: (res) => !res?.hasMore,
    },
  );
};
