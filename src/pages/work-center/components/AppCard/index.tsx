import React from 'react';
import { IAppEntry } from '../../types';
import './index.less';

interface AppCardProps {
  app: IAppEntry;
  onClick: () => void;
}

const AppCard: React.FC<AppCardProps> = ({ app, onClick }) => {
  const { name, icon, badge, disabled } = app;

  const handleClick = () => {
    if (!disabled) {
      onClick();
    }
  };

  return (
    <div className={`app-card ${disabled ? 'app-card--disabled' : ''}`} onClick={handleClick}>
      <img className="app-card__icon" src={icon} alt={name} />
      <div className="app-card__name">{name}</div>
    </div>
  );
};

export default AppCard;
