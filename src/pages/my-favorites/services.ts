
import { useInfiniteScroll } from 'ahooks';
import {
  queryUserFavoriteFeedStoreList,
  IRecFavoriteFeedStoreListRequest,
  IRecFavoriteFeedStoreListResponse,
  IRecFeedStore,
  queryUserFavoriteProductList,
  IJUserProductRequest,
  IJUserProductResponse,
  IProjectModel,
} from '@/apis/user-recommend';
import { RecFeedDetail, queryUserCollectedFeedList } from '@/apis/feed-action';
import { TENANT_ID } from '@/common/constants';
import { getSevenDingUserInfo } from '@/utils/jsapi';

// 获取收藏的 Store 列表
export const getStoreFavorites = async (): Promise<{
  data: IRecFeedStore[];
  cursor: string;
  hasMore: boolean;
}> => {
  const requestParams: IRecFavoriteFeedStoreListRequest = {
    tenantId: TENANT_ID,
  };

  const response: IRecFavoriteFeedStoreListResponse = await queryUserFavoriteFeedStoreList(requestParams);

  return {
    data: response.stores,
    cursor: response.nextCursor,
    hasMore: response.hasMore,
  };
};

// 使用 useInfiniteScroll 封装请求，支持无限滚动
export const useStoreFavorites = () => {
  return useInfiniteScroll(
    async () => {
      const result = await getStoreFavorites();
      return {
        list: result.data,
        cursor: result.cursor,
        hasMore: result.hasMore,
      };
    },
    {
      isNoMore: (res) => !res?.hasMore,
    },
  );
};

// 获取收藏的商品列表
export const getProductFavorites = async (params: {
  cursor?: string;
  size?: number;
}): Promise<{
  data: IProjectModel[];
  cursor: string;
  hasMore: boolean;
}> => {
  const { token } = await getSevenDingUserInfo();

  const requestParams: IJUserProductRequest = {
    cursor: params.cursor,
    size: params.size || 20,
    hhoToken: token,
  };

  const response: IJUserProductResponse = await queryUserFavoriteProductList(requestParams);

  return {
    data: response.products,
    cursor: response.nextCursor,
    hasMore: response.hasMore,
  };
};

// 使用 useInfiniteScroll 封装商品收藏请求，支持无限滚动
export const useProductFavorites = () => {
  return useInfiniteScroll(
    async (params: { list?: IProjectModel[]; cursor?: string; size?: number } = {}) => {
      const result = await getProductFavorites(params);
      return {
        list: result.data,
        cursor: result.cursor,
        hasMore: result.hasMore,
      };
    },
    {
      isNoMore: (res) => !res?.hasMore,
    },
  );
};

// 获取收藏的 Feed 列表
export const getCollectedFeedList = async (params: {
  cursor?: string;
  size?: number;
}): Promise<{
  data: RecFeedDetail[];
  cursor: string;
  hasMore: boolean;
}> => {
  const requestParams = {
    tenantId: TENANT_ID,
    cursor: params.cursor,
    size: params.size || 20,
  };

  const response = await queryUserCollectedFeedList(requestParams);

  return {
    data: response.feeds,
    cursor: response.cursor,
    hasMore: response.hasMore,
  };
};

// 使用 useInfiniteScroll 封装收藏 Feed 请求，支持无限滚动
export const useCollectedFeedList = () => {
  return useInfiniteScroll(
    async (params: { list?: RecFeedDetail[]; cursor?: string; size?: number } = {}) => {
      const result = await getCollectedFeedList(params);
      return {
        list: result.data,
        cursor: result.cursor,
        hasMore: result.hasMore,
      };
    },
    {
      isNoMore: (res) => !res?.hasMore,
    },
  );
};

// 生成产品详情链接
export const generateProductUrl = (product: RecFeedDetail): string => {
  // 使用 dingtalk 协议拼接 URL，itemId 作为 feedId
  return `dingtalk://dingtalkclient/page/j_feed_detail?feedId=${product.itemId}`;
};
