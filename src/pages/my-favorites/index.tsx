
import React, { useState } from 'react';
import TrendingView from './components/TrendingView';
import StoreView from './components/StoreView';
import ProductView from './components/ProductView';
import { Tabs } from 'dingtalk-design-mobile';
import { useTitle } from '@/hooks/useTitle';
import { i18next } from '@ali/dingtalk-i18n';
import './index.less';


function MyFavorites() {
  useTitle(i18next.t('j-dingtalk-web_pages_my-favorites_MyCollection'));

  const tabs = [
    { title: i18next.t('j-dingtalk-web_pages_my-favorites_Video'), key: 'trending' },
    { title: i18next.t('j-dingtalk-web_pages_my-favorites_Shop'), key: 'store' },
    { title: i18next.t('j-dingtalk-web_pages_my-favorites_Commodity'), key: 'product' }];


  return (
    <div className="my-favorites-page">
      <Tabs
        className="my-favorites-tabs"
        tabs={tabs}
        hideBottomDivider
      >

        <div className="my-favorites-tab-content" key="1">
          <TrendingView />
        </div>
        <div className="my-favorites-tab-content" key="2">
          <StoreView />
        </div>
        <div className="my-favorites-tab-content" key="3">
          <ProductView />
        </div>
      </Tabs>
    </div>
  );
}

export default MyFavorites;
