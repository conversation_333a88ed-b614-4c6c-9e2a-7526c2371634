import React from 'react';
import { openLink, isDingTalk } from '@/utils/jsapi';
import { getAIImageUrl, getAIVideoUrl } from '@/utils/env';
import { PictureOutlined, RightArrowLOutlined, VideoPlayOutlined } from '@ali/ding-icons';
import './index.less';

const AIStudioPage: React.FC = () => {
  // 处理 AI 画像生成点击事件
  const handleAIImageClick = () => {
    const url = getAIImageUrl();
    if (isDingTalk()) {
      openLink(url);
    } else {
      // 在浏览器环境中直接跳转
      window.location.href = url;
    }
  };

  // 处理 AI 视频生成点击事件
  const handleAIVideoClick = () => {
    const url = getAIVideoUrl();
    if (isDingTalk()) {
      openLink(url);
    } else {
      // 在浏览器环境中直接跳转
      window.location.href = url;
    }
  };

  return (
    <div className="ai-studio-page">
      <div className="container">
        {/* 主要内容 */}
        <div className="main-content">
          <img className="main-logo" src="https://img.alicdn.com/imgextra/i3/O1CN01a9s5S31GHTXGRhRgq_!!6000000000597-2-tps-192-192.png" alt="logo" />
          <h1 className="page-title">AI素材设计</h1>
        </div>

        {/* 功能卡片 */}
        <div className="feature-cards">
          {/* AI 画像生成卡片 */}
          <div className="feature-card" onClick={handleAIImageClick}>
            <div className="card-content">
              <PictureOutlined className="card-icon" />
              <div className="card-title">
                AI画像生成
                <RightArrowLOutlined className="arrow-icon" />
              </div>
              <div className="card-description">
                背景置換や自動切り抜きなどの機能により、あなたの画像をより美しく仕上げます
              </div>
            </div>
          </div>

          {/* AI 视频生成卡片 */}
          <div className="feature-card" onClick={handleAIVideoClick}>
            <div className="card-content">
              <VideoPlayOutlined className="card-icon" />
              <div className="card-title">
                AIによる動画生成
                <RightArrowLOutlined className="arrow-icon" />
              </div>
              <div className="card-description">
                高品質なEC商品動画の作成は、これまでになく簡単になりました
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AIStudioPage;
