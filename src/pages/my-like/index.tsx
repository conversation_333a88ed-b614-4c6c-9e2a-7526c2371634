import { i18next } from '@ali/dingtalk-i18n';
import React from 'react';
import { RecFeedDetail } from '@/apis/feed-action';
import { useLikeProductList, generateProductUrl } from './services';
import $openLink from '@ali/dingtalk-jsapi/api/biz/util/openLink';
import { InfiniteList } from '@/components/InfiniteList';
import ProductCard from './components/ProductCard';
import { useTitle } from '@/hooks/useTitle';
import { LikeLOutlined } from '@ali/ding-icons';
import './index.less';

const MyLikePage: React.FC = () => {
  const {
    data: response,
    loading,
    error,
    loadMore,
    loadingMore,
  } = useLikeProductList();


  const products = response?.list || [];
  const hasMore = response?.hasMore || false;
  const cursor = response?.cursor || undefined;

  useTitle(i18next.t('j-dingtalk-web_pages_my-like_ILikeIt'));

  // 加载更多
  const handleLoadMore = () => {
    if (hasMore && !loading && cursor !== undefined) {
      loadMore();
    }
  };

  // 点击跳转
  const handleProductClick = (product: RecFeedDetail) => {
    const url = generateProductUrl(product);
    $openLink({ url });
  };

  return (
    <InfiniteList
      data={products}
      renderItem={(item) =>
        <ProductCard key={item.itemId} product={item} onProductClick={handleProductClick} />
      }
      loading={loading || loadingMore}
      error={!!error}
      hasMore={hasMore}
      layout="waterfall"
      onLoadMore={handleLoadMore}
      emptyIcon={<LikeLOutlined />}
      emptyText={i18next.t('j-dingtalk-web_pages_my-like_IDonTLikeThe')}
      errorText={i18next.t('j-dingtalk-web_pages_my-like_NetworkErrorPleaseTryAgain')}
    />);
};

export default MyLikePage;
