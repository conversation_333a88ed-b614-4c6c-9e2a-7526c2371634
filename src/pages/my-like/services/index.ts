import { useInfiniteScroll } from 'ahooks';
import {
  queryUserLikeFeedList,
  RecUserLikeFeedQueryModel,
  RecUserLikeFeedQueryResult,
  RecFeedDetail,
} from '@/apis/feed-action';
import { TENANT_ID } from '@/common/constants';

// 获取点赞商品列表
export const getLikeProductList = async (params: {
  cursor?: string;
  size?: number;
}): Promise<{
  data: RecFeedDetail[];
  cursor: string;
  hasMore: boolean;
}> => {
  const requestParams: RecUserLikeFeedQueryModel = {
    tenantId: TENANT_ID, // 使用常量
    cursor: params.cursor,
    size: params.size || 20,
  };

  const response: RecUserLikeFeedQueryResult = await queryUserLikeFeedList(requestParams);

  return {
    data: response.feeds,
    cursor: response.cursor,
    hasMore: response.hasMore,
  };
};

export const useLikeProductList = () => {
  return useInfiniteScroll(
    async (params: { list?: RecFeedDetail[]; cursor?: string; size?: number } = {}) => {
      const result = await getLikeProductList(params);
      return {
        list: result.data,
        cursor: result.cursor,
        hasMore: result.hasMore,
      };
    },
    {
      isNoMore: (res) => !res?.hasMore,
    },
  );
};

// 生成产品详情链接
export const generateProductUrl = (product: RecFeedDetail): string => {
  // 使用 dingtalk 协议拼接 URL，itemId 作为 feedId
  return `dingtalk://dingtalkclient/page/j_feed_detail?feedId=${product.itemId}`;
};
