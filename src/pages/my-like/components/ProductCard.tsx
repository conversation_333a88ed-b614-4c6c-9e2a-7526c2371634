import React, { FC, useEffect, useRef, useState } from 'react';
import { i18next } from '@ali/dingtalk-i18n';
import { RecFeedDetail } from '@/apis/feed-action';
import { IFeedPayload } from '@/common/types';
import './ProductCard.less';

interface ProductCardProps {
  product: RecFeedDetail;
  onProductClick: (product: RecFeedDetail) => void;
}

const ProductCard: FC<ProductCardProps> = ({ product, onProductClick }) => {
  const imageRef = useRef<HTMLDivElement>(null);
  const [imageHeight, setImageHeight] = useState('100%'); // 默认 1:1
  const [showImage, setShowImage] = useState(false);

  // 解析 payload 字段
  const parsePayload = () => {
    try {
      return JSON.parse(product.payload || '{}');
    } catch (e) {
      console.warn(i18next.t('j-dingtalk-web_pages_my-like_components_ProductCard_FailedToParseProductPayload'), e);
      return {};
    }
  };

  const payload: IFeedPayload = parsePayload();
  const goods = payload?.goods;

  // 获取商品信息
  const productTitle = goods?.name || product.itemName || '';
  const productIntro = goods?.intro || product?.itemName || '';
  const productImage = goods?.image || product?.coverImg || '';

  useEffect(() => {
    // 优先使用数据中的图片宽高信息
    if (productImage) {
      // 如果没有宽高信息，则动态获取图片尺寸
      const tempImg = new Image();
      tempImg.referrerPolicy = 'no-referrer';
      tempImg.onload = () => {
        const { naturalWidth, naturalHeight } = tempImg;
        const aspectRatio = naturalHeight / naturalWidth;

        // 限制高度比例在 1:1 到 3:2 之间
        const minRatio = 1; // 最小 1:1
        const maxRatio = 1.5; // 最大 3:2 (9/6)

        const clampedRatio = Math.max(minRatio, Math.min(maxRatio, aspectRatio));
        setImageHeight(`${clampedRatio * 100}%`);
        setShowImage(true);
      };
      tempImg.src = productImage;
    }
  }, [productImage]);

  const handleClick = () => {
    onProductClick(product);
  };

  return (
    <div className="product-card" onClick={handleClick}>
      <div
        className="product-image-container"
        ref={imageRef}
        style={{ paddingBottom: imageHeight }}
      >
        {
          showImage &&
          <img
            src={productImage}
            alt={productTitle}
            className="product-image"
            referrerPolicy="no-referrer"
            loading="lazy"
          />
        }
      </div>
      <div className="product-info">
        <div className="product-title">
          {productIntro || productTitle}
        </div>
        {/* <div className="product-sales">
          </div> */}
        <div className="product-platform">
          {
            payload?.creator?.avatar &&
            <img className="creator-avatar" src={payload?.creator?.avatar} />
          }          <span className="platform-name">{payload?.creator?.nickName}</span>
        </div>
      </div>
    </div>);
};

export default ProductCard;
