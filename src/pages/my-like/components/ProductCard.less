@import (reference) "~dingtalk-theme/dingtalk-x/mob.less";

.product-card {
  background-color: @common_bg_z1_color;
  border-radius: @common_border_radius_m;
  overflow: hidden;
  cursor: pointer;

  .product-image-container {
    position: relative;
    width: 100%;
    padding-bottom: 100%;
    background: @common_bg_color;
  }

  .product-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
  }

  .product-info {
    padding: 12px;

    .product-title {
      .common_body_text_style_mob();
      color: @common_level1_base_color;
      margin-bottom: 8px;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .product-price {
      .common_h4_bold_text_style_mob();
      color: @theme_danger1_color;
      margin-bottom: 6px;
    }

    .product-sales {
      .common_footnote_text_style_mob();
      color: @common_red1_color;
      margin-bottom: 8px;
      font-size: 12px;
    }

    .product-platform {
      display: flex;
      align-items: center;
      gap: 4px;
      margin-top: 20px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;

      .creator-avatar {
        width: 16px;
        height: 16px;
        border-radius: 50%;
        object-fit: cover;
      }

      .platform-name {
        .common_footnote_text_style_mob();
        color: @common_level3_base_color;
        font-weight: 500;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        margin-left: 4px;
      }
    }
  }
}

@media (max-width: 768px) {
  .product-card {
    .product-info {
      padding: 8px;

      .product-title {
        .common_action_text_style_mob();
      }

      .product-price {
        .common_h5_bold_text_style_mob();
      }
    }
  }
}
