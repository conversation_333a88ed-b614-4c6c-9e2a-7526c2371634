body {
  background-color: var(--common_fg_press_color);
}

.productOrigin-page-container {
  background-color: var(--common_fg_press_color);
  width: 100%;
  margin-left: auto;
  box-sizing: border-box;
  margin-right: auto;
  display: block;
  padding: 16px 16px 24px 16px;
  padding-bottom: calc(env(safe-area-inset-bottom) + 24px);
  min-height: 100vh;

  .productOrigin-list {
    background-color: var(--common_fg_color);
    border-radius: 16px;
    padding: 12px;
    margin-top: 12px;
    img {
      width: 100%;
    }
  }

  .listTitle {
    margin-top: 12px;
    font-size: 20px;
    font-weight: 600;
    line-height: 28px;
    letter-spacing: normal;
    color: var(--common_level1_base_color);
  }

  .productOrigin-container {
    background-color: var(--common_fg_color);
    border-radius: 16px;
    padding: 12px;
  }

  .productOrigin {
    display: flex;
    margin-bottom: 12px;
    cursor: pointer;
    position: relative;

    &:last-child {
      margin-bottom: 0;
    }

    &.reported {
      cursor: not-allowed;

      &::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(255, 255, 255, 0.2);
        border-radius: 6px;
        z-index: 10;
        pointer-events: none;
      }

      .img-container,
      .discription-container {
        opacity: 0.5;
        filter: grayscale(100%);
      }

      .warning-icon {
        display: none;
      }
    }

    .img-container {
      position: relative;
      overflow: hidden;
      margin-right: 8px;
      width: 120px;
      height: 120px;
      border-radius: 6px;
      font-size: 0;
      .warning-icon {
        position: absolute;
        bottom: 8px;
        left: 8px;
        font-size: 24px;
        color: #fff;
        background-color: rgba(0, 0, 0, 0.6);
        border-radius: 50%;
        padding: 0 4px;
        backdrop-filter: blur(10px);
        cursor: pointer;
        z-index: 11;
      }

      .audit-status-badge {
        position: absolute;
        top: 8px;
        right: 8px;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        font-weight: bold;
        color: #fff;
        z-index: 11;

        &.audited,
        &.approved {
          color: rgba(34, 197, 94, 1); // Green for approved
        }

        &.unaudited,
        &.rejected {
          color: rgba(239, 68, 68, 1); // Red for rejected
        }

        &.unaudited {
          color: rgba(156, 163, 175, 1); // Gray for unaudited
        }
      }
    }

    .discription-container {
      flex: 1 1;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .price-title {
        color: var(--common_level1_base_color);
        font-size: 10px;
        font-style: normal;
        font-weight: 300;
        line-height: 14px;
      }

      .price {
        color: var(--common_level1_base_color);
        font-size: 12px;
        font-style: normal;
        font-weight: 300;

        span {
          color: #FF0E53;
          font-size: 20px;
          font-style: normal;
          font-weight: 600;
        }
      }

      .shopName {
        color: var(--common_level2_base_color);
        margin-top: 8px;
        font-size: 12px;
        font-style: normal;
      }

      .tag-wrap {
        display: flex;
        margin-top: 4px;
        width: 100%;
        align-items: center;
        justify-content: start;

        .product-stats {
          max-width: 100%;
          color: #666;
          font-size: 0;
          .value {
            margin-right: 6px;
            margin-bottom: 4px;
            max-width: 100%;
            display: inline-block;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            padding: 2.5px 4px;
            font-size: 12px;
            border: 0.5px solid #FF0E53;
            border-radius: 4px;
            color: var(--extended_red5_color, #FF0E53);
          }
        }
      }

      .product-title {
        width: 100%;
        color: var(--common_level1_base_color);
        text-overflow: ellipsis;
        font-family: Hiragino Sans;
        white-space: wrap;
        overflow: hidden;
        -webkit-line-clamp: 2;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        font-size: 13px;
        font-style: normal;
        font-weight: 300;
        line-height: 18px;
      }

      .product-detail-bottom {
        position: relative;
      }

      .product-price {
        display: flex;
        flex-direction: column;

        .origin {
          color: #666;
          font-family: Hiragino Sans;
          font-size: 10px;
          font-style: normal;
          font-weight: 300;
          line-height: 14px;
        }

        .price {
          font-size: 20px;
          color: #FF0E53;
          font-weight: 600;
        }
      }
    }
  }

  .product-detail {
    border-radius: 12px;
    margin-bottom: 8px;

    .product-detail-title {
      color: var(--common_level1_base_color);
    }

    .detail-content {
      margin-top: 12px;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 12px;

      .item {
        border-right: 1px solid var(--common_line_light_color);
        flex: 1;

        &:first-child {
          flex: 0.8;
        }
        &:last-child {
          flex: 1.2;
          border-right: none;
        }

        .discription {
          color: var(--common_level1_base_color);
          font-size: 12px;
          font-style: normal;
          font-weight: 300;
          line-height: normal;
          margin-bottom: 4px;
        }

        .dis-content {
          font-size: 14px;
          font-weight: 500;
          line-height: 20px;
          color: var(--common_level1_base_color);
          white-space: nowrap;
        }
      }
    }
  }

  .product-detail-line {
    margin-top: 12px;

    .title {
      color: var(--common_level1_base_color);
      font-size: 12px;
      font-style: normal;
      font-weight: 300;
      line-height: normal;
      margin-bottom: 4px;
    }

    .content {
      color: var(--common_level1_base_color);
      font-size: 16px;
      font-style: normal;
      font-weight: 600;
      line-height: normal;
      display: flex;
      align-items: center;
    }
  }

  // Audit mask overlay for product items
  .audit-mask {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    z-index: 10;

    .audit-buttons {
      display: flex;
      gap: 12px;

      .audit-btn {
        min-width: 80px;
        height: 36px;
        line-height: 36px;
        border-radius: 18px;
        font-size: 14px;
        font-weight: 500;
        border: none;
        transition: all 0.2s ease;

        &.approve-btn {
          background: #52c41a;
          color: white;

          &:hover {
            background: #73d13d;
          }

          &:active {
            background: #389e0d;
          }
        }

        &.reject-btn {
          background: #ff4d4f;
          color: white;

          &:hover {
            background: #ff7875;
          }

          &:active {
            background: #d9363e;
          }
        }
      }
    }
  }
}