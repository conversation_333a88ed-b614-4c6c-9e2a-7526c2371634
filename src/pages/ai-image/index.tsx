import { i18next } from '@ali/dingtalk-i18n';
import React, { useEffect, useState, useRef } from 'react';
import { Toast } from 'dingtalk-design-mobile';
import { isDingTalk, isPc, isMobileDevice, setPageTitle } from '@/utils/jsapi';
import { sendUT } from '@/utils/trace';
import { generateImage, checkImageStatus, listImages } from '@/apis/image';
import $setRight from '@ali/dingtalk-jsapi/api/biz/navigation/setRight';
import $setShare from '@ali/dingtalk-jsapi/api/biz/util/share';
import Loading from '@/components/Loading';
import WelcomeScreen from './components/WelcomeScreen';
import ImageForm from './components/ImageForm';
import ImageList from './components/ImageList';
import './index.less';

// Image information interface
interface ImageInfo {
  uuid: string;
  imgUrls: string[];
  originImgUrl?: string; // 仅当生成类型时hd时有效，用于图片像素对比
  type: 'normal' | 'hd'; // 普通图片normal,高清图片：hd
  userRating?: string;
  status: string;
  errorMsg?: string;
  createAt: string;
  finishTime?: string;
  // 前端扩展字段
  prompt?: string;
  negativePrompt?: string;
  size?: string;
  background?: string;
  progress?: number;
  liked?: boolean;
}

// Main state interface for AI Image generation
interface AIImageState {
  currentStep: 'welcome' | 'form' | 'imageList';
  rightPanelContent: 'welcome' | 'imageList'; // For PC layout right panel
  uploadedImage: string | null;
  positivePrompt: string;
  negativePrompt: string;
  selectedBackground: string | null;
  imageSize: string;
  isGenerating: boolean;
  generatedImageUrl: string | null;
  error: string | null;
  taskId: string | null;
  progress: number;
  // Image list related states
  imageList: ImageInfo[];
  isLoadingImages: boolean;
  hasImages: boolean;
  imageListError: string | null;
  nextUuid: string | null;
  hasNextPage: boolean;
  progressMap: Record<string, number>; // Map of image UUID to progress percentage
  isLoadingMore: boolean; // Loading state for pagination
}

// Helper function to get image pixel size
const getImagePixel = (size: string): string => {
  switch (size) {
    case '1:1':
      return '800x800';
    case '3:4':
      return '750x1000';
    case '16:9':
      return '1280x720';
    default:
      return '800x800';
  }
};

const AIImagePage: React.FC = () => {
  const [state, setState] = useState<AIImageState>({
    currentStep: isMobileDevice() ? 'welcome' : 'form',
    rightPanelContent: 'welcome', // PC layout right panel starts with welcome
    uploadedImage: null,
    positivePrompt: '',
    negativePrompt: '',
    selectedBackground: null,
    imageSize: '1:1',
    isGenerating: false,
    generatedImageUrl: null,
    error: null,
    taskId: '',
    progress: 0,
    // Image list related states
    imageList: [],
    isLoadingImages: true, // Start with loading state
    hasImages: false,
    imageListError: null,
    nextUuid: null,
    hasNextPage: false,
    progressMap: {},
    isLoadingMore: false, // Loading state for pagination
  });

  const pollingTimersRef = useRef<Record<string, ReturnType<typeof setTimeout>>>({});
  const isUnmountedRef = useRef(false);

  // Utility function to scroll to top of image list
  const scrollToTop = () => {
    setTimeout(() => {
      if (isMobileDevice()) {
        // Mobile: scroll window to top
        window.scrollTo({ top: 0, behavior: 'smooth' });
      } else {
        // PC: scroll right panel to top
        const rightPanel = document.querySelector('.ai-image-right-panel') as HTMLElement;
        if (rightPanel) {
          rightPanel.scrollTo({ top: 0, behavior: 'smooth' });
        }
      }
    }, 100); // Small delay to ensure DOM is updated
  };

  useEffect(() => {
    // Set page title
    setPageTitle('AI画像生成');

    // Set share button
    setShare();

    // Send page view event
    sendUT('ai_image_page_view', {
      device: isMobileDevice() ? 'mobile' : 'pc',
    });

    // Load image list on component mount
    loadImageList();

    // Cleanup on unmount
    return () => {
      isUnmountedRef.current = true;
      // Clear all polling timers
      Object.values(pollingTimersRef.current).forEach((timer) => {
        clearTimeout(timer);
      });
      pollingTimersRef.current = {};
    };
  }, []);

  const setShare = () => {
    if (!isDingTalk() || isPc) {
      return;
    }

    $setRight({
      show: true,
      control: true,
      text: '•••',
      onSuccess: () => {
        $setShare({
          type: 0,
          url: window.location.href,
          title: 'AI画像生成',
          content: 'Generating high quality e-commerce product images has never been easier.',
          image: 'https://img.alicdn.com/imgextra/i1/O1CN01DfZoSi1cL4BQtyhAm_!!6000000003583-2-tps-192-192.png',
        });
      },
    });
  };

  // Handle form updates
  const handleFormUpdate = (updates: Partial<AIImageState>) => {
    setState((prev) => ({ ...prev, ...updates }));
  };

  // Handle step changes for mobile
  const handleStepChange = (step: 'welcome' | 'form' | 'imageList') => {
    setState((prev) => ({ ...prev, currentStep: step }));
  };

  // Handle create new image
  const handleCreateNewImage = () => {
    if (isMobileDevice()) {
      setState((prev) => ({ ...prev, currentStep: 'form' }));
    } else {
      setState((prev) => ({ ...prev, rightPanelContent: 'welcome' }));
    }
  };

  // Load image list
  const loadImageList = async (isLoadMore = false) => {
    try {
      if (!isLoadMore) {
        setState((prev) => ({ ...prev, isLoadingImages: true }));
      } else {
        setState((prev) => ({ ...prev, isLoadingMore: true }));
      }

      let result = await listImages({
        nextUuid: isLoadMore ? state.nextUuid : null,
        limit: 20,
      });

      result = {
        success: true,
        datas: JSON.stringify([
          {
            uuid: '1234567890',
            imgUrls: [
              'https://img.alicdn.com/imgextra/i1/O1CN01DfZoSi1cL4BQtyhAm_!!6000000003583-2-tps-192-192.png',
              'https://img.alicdn.com/imgextra/i1/O1CN01DfZoSi1cL4BQtyhAm_!!6000000003583-2-tps-192-192.png',
              'https://img.alicdn.com/imgextra/i1/O1CN01DfZoSi1cL4BQtyhAm_!!6000000003583-2-tps-192-192.png',
              'https://img.alicdn.com/imgextra/i1/O1CN01DfZoSi1cL4BQtyhAm_!!6000000003583-2-tps-192-192.png',
            ],
            originImgUrl: 'https://img.alicdn.com/imgextra/i1/O1CN01DfZoSi1cL4BQtyhAm_!!6000000003583-2-tps-192-192.png',
            type: 'normal',
            userRating: 'like',
            status: 'completed',
            errorMsg: null,
            createAt: '2023-08-11T07:22:12.000Z',
            finishTime: '2023-08-11T07:22:12.000Z',
            prompt: 'a cute cat',
            negativePrompt: '',
            size: '1:1',
            background: 'white',
            progress: 100,
            liked: true,
          },
        ]),
        nextUuid: null,
        hasNext: false,
      };

      if (result && result.success && result.datas) {
        const imageList = JSON.parse(result.datas) || [];
        const nextUuid = result.nextUuid || null;
        const hasNextPage = !!nextUuid;

        setState((prev) => ({
          ...prev,
          imageList: isLoadMore ? [...prev.imageList, ...imageList] : imageList,
          hasImages: imageList.length > 0,
          isLoadingImages: false,
          isLoadingMore: false,
          nextUuid,
          hasNextPage,
          currentStep: imageList.length > 0 && isMobileDevice() ? 'imageList' : prev.currentStep,
          rightPanelContent: imageList.length > 0 && !isMobileDevice() ? 'imageList' : 'welcome',
        }));

        // Start polling for images that need it
        checkAndStartPollingForImages(imageList);
      } else {
        // Handle empty image list
        setState((prev) => ({
          ...prev,
          imageList: [],
          hasImages: false,
          isLoadingImages: false,
          isLoadingMore: false,
          nextUuid: null,
          hasNextPage: false,
          currentStep: isMobileDevice() ? 'welcome' : 'form',
          rightPanelContent: 'welcome',
        }));
      }
    } catch (error) {
      // Handle API error silently, log for debugging if needed
      setState((prev) => ({
        ...prev,
        isLoadingImages: false,
        isLoadingMore: false,
        imageListError: error instanceof Error ? error.message : 'Failed to load images',
        hasImages: false,
        // Keep original welcome/form logic when API fails
        currentStep: isMobileDevice() ? 'welcome' : 'form',
        rightPanelContent: 'welcome',
      }));
    }
  };

  // Load more images for pagination
  const loadMoreImages = () => {
    if (state.hasNextPage && !state.isLoadingMore) {
      loadImageList(true);
    }
  };

  // Refresh image list
  const handleRefreshImageList = () => {
    loadImageList();
  };

  // Poll image status
  const pollImageStatus = async (uuid: string, attempt = 0, isCurrentlyGenerating = false) => {
    if (isUnmountedRef.current) return;

    try {
      const result = await checkImageStatus({ uuid });

      if (result && result.success && result.data) {
        const imageData = result.data;
        const progress = imageData.progress || 0;

        // Update progress in progressMap
        setState((prev) => ({
          ...prev,
          progressMap: {
            ...prev.progressMap,
            [uuid]: progress,
          },
          // Update current generation progress if this is the currently generating image
          progress: isCurrentlyGenerating ? progress : prev.progress,
        }));

        if (imageData.status === 'completed') {
          // Image generation completed
          setState((prev) => ({
            ...prev,
            progressMap: {
              ...prev.progressMap,
              [uuid]: 100,
            },
            isGenerating: isCurrentlyGenerating ? false : prev.isGenerating,
            generatedImageUrl: isCurrentlyGenerating
              ? imageData.imgUrls?.[0]
              : prev.generatedImageUrl,
            progress: isCurrentlyGenerating ? 100 : prev.progress,
          }));

          // Clear polling timer
          if (pollingTimersRef.current[uuid]) {
            clearTimeout(pollingTimersRef.current[uuid]);
            delete pollingTimersRef.current[uuid];
          }

          // Update image list
          setState((prev) => ({
            ...prev,
            imageList: prev.imageList.map((img) =>
              (img.uuid === uuid ? { ...img, ...imageData, status: 'completed' } : img)),
          }));

          // Show success toast for currently generating image
          if (isCurrentlyGenerating) {
            Toast.success({
              content: 'Image generated successfully!',
              position: 'top',
              maskClickable: true,
              duration: 2,
            });
          }
        } else if (imageData.status === 'failed') {
          // Image generation failed
          setState((prev) => ({
            ...prev,
            progressMap: {
              ...prev.progressMap,
              [uuid]: 0,
            },
            isGenerating: isCurrentlyGenerating ? false : prev.isGenerating,
            error: isCurrentlyGenerating ? (imageData.errorMsg || 'Image generation failed') : prev.error,
            progress: isCurrentlyGenerating ? 0 : prev.progress,
          }));

          // Clear polling timer
          if (pollingTimersRef.current[uuid]) {
            clearTimeout(pollingTimersRef.current[uuid]);
            delete pollingTimersRef.current[uuid];
          }

          // Update image list
          setState((prev) => ({
            ...prev,
            imageList: prev.imageList.map((img) =>
              (img.uuid === uuid ? { ...img, ...imageData, status: 'failed' } : img)),
          }));

          // Show error toast for currently generating image
          if (isCurrentlyGenerating) {
            Toast.fail({
              content: imageData.errorMsg || 'Image generation failed',
              position: 'top',
              maskClickable: true,
              duration: 3,
            });
          }
        } else {
          // Still processing, continue polling
          const nextAttempt = attempt + 1;
          const delay = Math.min(2000 + nextAttempt * 1000, 10000); // Exponential backoff, max 10s

          pollingTimersRef.current[uuid] = setTimeout(() => {
            pollImageStatus(uuid, nextAttempt, isCurrentlyGenerating);
          }, delay);
        }
      }
    } catch (error) {
      // Handle polling error
      // console.error('Failed to check image status:', error);

      // Clear polling timer
      if (pollingTimersRef.current[uuid]) {
        clearTimeout(pollingTimersRef.current[uuid]);
        delete pollingTimersRef.current[uuid];
      }

      if (isCurrentlyGenerating) {
        setState((prev) => ({
          ...prev,
          isGenerating: false,
          error: 'Failed to check image status',
          progress: 0,
        }));
      }

      Toast.fail({
        content: 'Failed to query image status',
        position: 'top',
        maskClickable: true,
        duration: 3,
      });
    }
  };

  // Start polling for a specific image
  const startPollingForImage = (uuid: string, isCurrentlyGenerating = false) => {
    // Don't start polling if already polling this image
    if (pollingTimersRef.current[uuid]) {
      return;
    }
    pollImageStatus(uuid, 0, isCurrentlyGenerating);
  };

  // Check image list and start polling for images that need it
  const checkAndStartPollingForImages = (imageList: ImageInfo[]) => {
    imageList.forEach((image) => {
      if ((image.status === 'pending' || image.status === 'processing') && !pollingTimersRef.current[image.uuid]) {
        startPollingForImage(image.uuid);
      }
    });
  };

  // Handle image generation
  const handleGenerateImage = async () => {
    if (state.isGenerating) return;

    // Validation
    if (!state.uploadedImage) {
      Toast.info({
        content: 'Please upload an image first',
        position: 'top',
        maskClickable: true,
        duration: 2,
      });
      return;
    }

    if (!state.positivePrompt.trim()) {
      Toast.info({
        content: 'Please enter a creative description',
        position: 'top',
        maskClickable: true,
        duration: 2,
      });
      return;
    }

    setState((prev) => ({
      ...prev,
      isGenerating: true,
      error: null,
      progress: 0,
    }));

    try {
      // Send generate event
      sendUT('ai_image_generate_click', {
        device: isMobileDevice() ? 'mobile' : 'pc',
        size: state.imageSize,
        positivePrompt: state.positivePrompt,
        negativePrompt: state.negativePrompt,
        background: state.selectedBackground,
        image: state.uploadedImage,
      });

      // Call image generation API
      const result = await generateImage({
        imgUrl: state.uploadedImage,
        requirements: state.positivePrompt,
        imgPixel: getImagePixel(state.imageSize),
      });

      if (result && result.success && result.data) {
        const { uuid } = result.data;

        setState((prev) => ({
          ...prev,
          taskId: uuid,
          // Switch to image list view
          currentStep: isMobileDevice() ? 'imageList' : prev.currentStep,
          rightPanelContent: !isMobileDevice() ? 'imageList' : prev.rightPanelContent,
        }));

        // Refresh image list to include the new generating image
        await loadImageList();

        // Scroll to top when switching to image list
        scrollToTop();

        // Start polling for image generation status
        startPollingForImage(uuid, true);
      } else {
        throw new Error(result?.errorMsg || 'Failed to generate image');
      }
    } catch (error) {
      // console.error('Image generation failed:', error);
      setState((prev) => ({
        ...prev,
        isGenerating: false,
        error: error instanceof Error ? error.message : 'Image generation failed, please try again',
        progress: 0,
      }));
      Toast.fail({
        content: 'Image generation failed, please try again',
        position: 'top',
        maskClickable: true,
        duration: 3,
      });
    }
  };

  // Handle regenerate from list
  const handleRegenerateFromList = (imageInfo: ImageInfo) => {
    // Set form data from the image info
    setState((prev) => ({
      ...prev,
      uploadedImage: imageInfo.imgUrls?.[0] || imageInfo.originImgUrl,
      positivePrompt: imageInfo.prompt || '',
      negativePrompt: imageInfo.negativePrompt || '',
      imageSize: imageInfo.size || '1:1',
      selectedBackground: imageInfo.background || null,
      currentStep: isMobileDevice() ? 'form' : prev.currentStep,
      rightPanelContent: !isMobileDevice() ? 'welcome' : prev.rightPanelContent,
    }));
  };

  // Render PC layout with left-right structure
  const renderPCLayout = () => {
    // Show loading overlay if images are loading
    if (state.isLoadingImages) {
      return (
        <div className="ai-image-pc-layout">
          <Loading text={i18next.t('j-dingtalk-web_pages_aiVideo_Loading')} />
        </div>
      );
    }

    return (
      <div className="ai-image-pc-layout">
        {/* Left Panel - Always show ImageForm */}
        <div className="ai-image-left-panel">
          <ImageForm
            state={state}
            onUpdate={handleFormUpdate}
            onGenerate={handleGenerateImage}
          />
        </div>

        {/* Right Panel - Show Welcome or ImageList based on state */}
        <div className="ai-image-right-panel">
          {state.rightPanelContent === 'welcome' && (
            <WelcomeScreen
              hasGenerateBtn={false}
              onGetStarted={() => {
                // On PC, clicking "Get Started" doesn't change layout, just focuses on form
                // Could add some visual feedback here if needed
              }}
            />
          )}

          {state.rightPanelContent === 'imageList' && (
            <ImageList
              imageList={state.imageList}
              isLoading={false} // Loading is handled at page level
              error={state.imageListError}
              onCreateNew={handleCreateNewImage}
              onRegenerate={handleRegenerateFromList}
              onRefresh={handleRefreshImageList}
              progressMap={state.progressMap}
              hasNextPage={state.hasNextPage}
              isLoadingMore={state.isLoadingMore}
              onLoadMore={loadMoreImages}
              loadImageList={loadImageList}
            />
          )}
        </div>
      </div>
    );
  };

  // Render mobile layout
  const renderMobileLayout = () => {
    // Show loading screen if images are loading
    if (state.isLoadingImages) {
      return <Loading text={i18next.t('j-dingtalk-web_pages_aiVideo_Loading')} />;
    }

    switch (state.currentStep) {
      case 'welcome':
        return (
          <WelcomeScreen
            hasGenerateBtn
            onGetStarted={() => handleStepChange('form')}
          />
        );
      case 'form':
        return (
          <ImageForm
            state={state}
            onUpdate={handleFormUpdate}
            onGenerate={handleGenerateImage}
          />
        );
      case 'imageList':
        return (
          <ImageList
            imageList={state.imageList}
            isLoading={false} // Loading is handled at page level
            error={state.imageListError}
            onCreateNew={handleCreateNewImage}
            onRegenerate={handleRegenerateFromList}
            onRefresh={handleRefreshImageList}
            progressMap={state.progressMap}
            hasNextPage={state.hasNextPage}
            isLoadingMore={state.isLoadingMore}
            onLoadMore={loadMoreImages}
            loadImageList={loadImageList}
          />
        );
      default:
        return null;
    }
  };

  return (
    <div className="ai-image-page">
      {isMobileDevice() ? renderMobileLayout() : renderPCLayout()}
    </div>
  );
};

export default AIImagePage;
