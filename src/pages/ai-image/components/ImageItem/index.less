.image-item {
  width: 100%;
  background-color: #000000;
  overflow: hidden;
  margin-bottom: 16px;
  transition: all 0.3s ease;

  .image-item-header {
    padding: 16px 0 10px 0;

    .prompt-info-container {
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      cursor: pointer;

      .prompt-info {
        flex: 1;
        color: rgba(255, 255, 255, 0.9);
        font-size: 16px;
        line-height: 22px;
        word-break: break-word;
        transition: all 0.3s ease;

        // Collapsed state: single line with ellipsis
        &.collapsed {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        // Expanded state: full text display
        &.expanded {
          white-space: normal;
          overflow: visible;
        }
      }

      .prompt-toggle-icon {
        margin-left: 8px;
        color: white;
        font-size: 18px;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 22px;
        height: 22px;
        flex-shrink: 0;
        cursor: pointer;
        transition: all 0.3s ease;
        transform-origin: center center;

        &:hover {
          color: rgba(255,255,255,0.8);
        }

        &.expanded {
          transform: rotate(180deg);
        }
      }
    }
  }

  .image-content {
    position: relative;
    width: 100%;
    height: 316px; // Fixed height to accommodate 4-image grid: 150px * 2 + 8px + padding
    border-radius: 12px;
    overflow: hidden;

    .image-container {
      position: relative;
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;

      .image-grid {
        display: grid;
        grid-template-columns: 168px 168px;
        grid-template-rows: 150px 150px;
        gap: 8px;

        .image-preview {
          width: 168px;
          height: 150px;
          object-fit: cover;
          border-radius: 8px;
        }
      }
    }

    .image-placeholder {
      position: relative;
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;

      .status-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        backdrop-filter: blur(3px);
        -webkit-backdrop-filter: blur(3px);
        background: rgba(0, 0, 0, 0.7);
        color: white;
        font-size: 14px;
        font-weight: 500;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: 12px;

        .status-text {
          margin: 0;
        }

        .status-processing {
          display: flex;
          flex-direction: column;
          align-items: center;
          width: 80%;
          gap: 12px;

          .generating-text {
            color: white;
            font-size: 14px;
            font-weight: 600;
            line-height: 18px;
            text-align: center;
          }

          // 圆形进度条样式 - Circular progress styles
          .circular-progress {
            position: relative;
            display: inline-block;

            .progress-ring {
              transform: rotate(-90deg);

              .progress-ring-circle {
                transition: stroke-dashoffset 0.3s ease;
                stroke: white;
              }
            }

            .progress-text {
              position: absolute;
              top: 50%;
              left: 50%;
              transform: translate(-50%, -50%);
              font-size: 12px;
              font-weight: 600;
              color: white;
            }
          }

          // 生成中的图片网格 - Generating image grid
          .image-grid-generating {
            display: grid;
            grid-template-columns: 80px 80px;
            grid-template-rows: 70px 70px;
            gap: 4px;

            .image-placeholder-item {
              background: linear-gradient(45deg, rgba(255, 255, 255, 0.1) 25%, transparent 25%),
                          linear-gradient(-45deg, rgba(255, 255, 255, 0.1) 25%, transparent 25%),
                          linear-gradient(45deg, transparent 75%, rgba(255, 255, 255, 0.1) 75%),
                          linear-gradient(-45deg, transparent 75%, rgba(255, 255, 255, 0.1) 75%);
              background-size: 20px 20px;
              background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
              border-radius: 4px;
              transition: all 0.3s ease;
            }
          }
        }

        .failed-reason {
          font-size: 12px;
          line-height: 16px;
          color: rgba(255, 255, 255, 0.4);
          text-align: center;
        }
      }
    }
  }

  // Action buttons section
  .action-section.pc-layout {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 0;

    .action-buttons {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      gap: 24px;
    }
  }

  .action-buttons.mobile-layout {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 24px;
    padding: 16px 0;
  }

  .action-button {
    background-color: transparent;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    color: rgba(255, 255, 255, 0.9);
    font-size: 24px;

    &:hover {
      transform: scale(1.05);
    }

    &:active {
      transform: scale(0.95);
    }

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }

    // Like button active state
    &.liked {
      color: #ffffff;
    }

    // Dislike button active state
    &.disliked {
      color: #ffffff;
    }
  }
}
