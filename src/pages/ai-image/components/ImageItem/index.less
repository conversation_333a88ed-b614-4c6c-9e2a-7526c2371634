.image-item {
  width: 100%;
  background-color: #000000;
  overflow: hidden;
  margin-bottom: 16px;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    transform: translateY(-2px);
  }

  .image-content {
    position: relative;
    width: 100%;
    aspect-ratio: 1;
    background-color: rgba(255, 255, 255, 0.05);
    overflow: hidden;

    // 完成状态的图片网格显示 - Completed state image grid display
    .image-container-completed {
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;

      .image-grid {
        flex: 1;
        display: grid;
        grid-template-columns: 1fr 1fr;
        grid-template-rows: 1fr 1fr;
        gap: 2px;

        .image-preview-grid {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
    }

    // 生成中状态显示 - Generating state display
    .image-generating,
    .image-failed {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: rgba(255, 255, 255, 0.05);

      .generating-overlay,
      .failed-overlay {
        text-align: center;
        padding: 20px;
        width: 100%;
      }

      .generating-text {
        font-size: 14px;
        font-weight: 600;
        line-height: 20px;
        color: rgba(255, 255, 255, 0.8);
        margin-bottom: 16px;
      }

      // 圆形进度条样式 - Circular progress styles
      .circular-progress {
        position: relative;
        display: inline-block;
        margin-bottom: 20px;

        .progress-ring {
          transform: rotate(-90deg);

          .progress-ring-circle {
            transition: stroke-dashoffset 0.3s ease;
            stroke: rgba(48, 128, 219, 1);
          }
        }

        .progress-text {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          font-size: 12px;
          font-weight: 600;
          color: rgba(48, 128, 219, 1);
        }
      }

      // 生成中的图片网格 - Generating image grid
      .image-grid-generating {
        display: grid;
        grid-template-columns: 1fr 1fr;
        grid-template-rows: 1fr 1fr;
        gap: 4px;
        width: 200px;
        height: 200px;

        .image-placeholder {
          background: linear-gradient(45deg, rgba(255, 255, 255, 0.1) 25%, transparent 25%),
                      linear-gradient(-45deg, rgba(255, 255, 255, 0.1) 25%, transparent 25%),
                      linear-gradient(45deg, transparent 75%, rgba(255, 255, 255, 0.1) 75%),
                      linear-gradient(-45deg, transparent 75%, rgba(255, 255, 255, 0.1) 75%);
          background-size: 20px 20px;
          background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
          border-radius: 4px;
          transition: all 0.3s ease;
        }
      }

      .failed-text {
        font-size: 14px;
        font-weight: 600;
        line-height: 20px;
        color: rgba(255, 14, 83, 1);
        margin-bottom: 8px;
      }

      .failed-reason {
        font-size: 12px;
        line-height: 16px;
        color: rgba(255, 255, 255, 0.4);
      }
    }
  }

  .image-info {
    padding: 16px;

    .image-prompt {
      font-size: 14px;
      line-height: 20px;
      color: rgba(255, 255, 255, 0.8);
      margin-bottom: 8px;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }

    .image-meta {
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 12px;
      line-height: 16px;
      color: rgba(255, 255, 255, 0.4);

      .image-size {
        background-color: rgba(48, 128, 219, 0.2);
        color: rgba(48, 128, 219, 1);
        padding: 2px 6px;
        border-radius: 4px;
        font-size: 10px;
      }
    }
  }

  .image-actions {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    border-top: 1px solid rgba(255, 255, 255, 0.16);

    .action-button {
      flex: 1;
      height: 36px;
      border: 1px solid rgba(255, 255, 255, 0.16);
      background-color: transparent;
      color: rgba(255, 255, 255, 0.6);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16px;
      border-radius: 6px;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        border-color: rgba(48, 128, 219, 1);
        color: rgba(48, 128, 219, 1);
        background-color: rgba(48, 128, 219, 0.1);
      }

      // 点赞按钮样式 - Like button styles
      &.liked {
        color: rgba(255, 14, 83, 1);
        border-color: rgba(255, 14, 83, 1);
        background-color: rgba(255, 14, 83, 0.1);

        &:hover {
          color: rgba(255, 14, 83, 0.8);
          border-color: rgba(255, 14, 83, 0.8);
        }
      }

      // 点踩按钮样式 - Dislike button styles
      &.disliked {
        color: rgba(255, 140, 0, 1);
        border-color: rgba(255, 140, 0, 1);
        background-color: rgba(255, 140, 0, 0.1);

        &:hover {
          color: rgba(255, 140, 0, 0.8);
          border-color: rgba(255, 140, 0, 0.8);
        }
      }
    }
  }
}

// Mobile responsive adjustments
@media (max-width: 768px) {
  .image-item {
    .image-info {
      padding: 12px;

      .image-prompt {
        font-size: 13px;
        line-height: 18px;
      }

      .image-meta {
        font-size: 11px;
      }
    }

    .image-actions {
      padding: 10px 12px;
      gap: 6px;

      .action-button {
        height: 32px;
        font-size: 14px;
      }
    }
  }
}
