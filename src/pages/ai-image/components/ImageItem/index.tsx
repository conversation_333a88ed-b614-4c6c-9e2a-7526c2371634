import React from 'react';
import { Toast } from 'dingtalk-design-mobile';
import {
  LikeOutlined,
  LikeFilled,
  DislikeOutlined,
  DislikeFilled,
  RefreshOutlined,
} from '@ali/ding-icons';
import { i18next } from '@ali/dingtalk-i18n';
import OptimizedImage from '@/components/OptimizedImage';
import { rateImage } from '@/apis/image';
import './index.less';

// 圆形进度条组件 - Circular progress component for image generation
const CircularProgress: React.FC<{progress: number}> = ({ progress }) => {
  return (
    <div className="circular-progress">
      <svg className="progress-ring" width="60" height="60">
        <circle
          className="progress-ring-circle"
          stroke="currentColor"
          strokeWidth="4"
          fill="transparent"
          r="26"
          cx="30"
          cy="30"
          style={{
            strokeDasharray: `${2 * Math.PI * 26}`,
            strokeDashoffset: `${2 * Math.PI * 26 * (1 - progress / 100)}`,
          }}
        />
      </svg>
      <div className="progress-text">{Math.round(progress)}%</div>
    </div>
  );
};

// Image information interface
interface ImageInfo {
  uuid: string;
  imgUrls: string[]; // 包含4张图片的数组 - Array containing 4 images
  originImgUrl?: string; // 仅当生成类型时hd时有效，用于图片像素对比
  type: 'normal' | 'hd'; // 普通图片normal,高清图片：hd
  userRating?: string;
  status: string;
  errorMsg?: string;
  createAt: string;
  finishTime?: string;
  // 前端扩展字段
  prompt?: string;
  negativePrompt?: string;
  size?: string;
  background?: string;
  progress?: number;
  liked?: boolean;
  disliked?: boolean; // 添加点踩状态 - Add dislike state
}

interface ImageItemProps {
  imageInfo: ImageInfo;
  onRegenerate: (imageInfo: ImageInfo) => void;
  className?: string;
  progress?: number; // Image generation progress (0-100)
  loadImageList: () => void; // Load image list
}

const ImageItem: React.FC<ImageItemProps> = ({
  imageInfo,
  onRegenerate,
  className = '',
  progress = 0,
  loadImageList,
}) => {
  // Handle like/unlike - 处理点赞/取消点赞
  const handleLike = async () => {
    try {
      const newLikedState = !imageInfo.liked;
      await rateImage({
        uuid: imageInfo.uuid,
        rating: newLikedState ? 'like' : 'unlike',
      });

      // Update local state (this would typically be handled by parent component)
      Toast.success({
        content: newLikedState ? i18next.t('j-dingtalk-web_pages_aiImage_components_ImageItem_Liked') : i18next.t('j-dingtalk-web_pages_aiImage_components_ImageItem_LikeCancelled'),
        position: 'top',
        duration: 1,
        maskClickable: true,
      });

      // Refresh the image list to update the state
      loadImageList();
    } catch (err) {
      Toast.fail({
        content: i18next.t('j-dingtalk-web_pages_aiImage_components_ImageItem_OperationFailed'),
        position: 'top',
        duration: 2,
        maskClickable: true,
      });
    }
  };

  // Handle dislike/unlike - 处理点踩/取消点踩
  const handleDislike = async () => {
    try {
      const newDislikedState = !imageInfo.disliked;
      await rateImage({
        uuid: imageInfo.uuid,
        rating: newDislikedState ? 'dislike' : 'unlike',
      });

      // Update local state (this would typically be handled by parent component)
      Toast.success({
        content: newDislikedState ? i18next.t('j-dingtalk-web_pages_aiImage_components_ImageItem_Disliked') : i18next.t('j-dingtalk-web_pages_aiImage_components_ImageItem_DislikeCancelled'),
        position: 'top',
        duration: 1,
        maskClickable: true,
      });

      // Refresh the image list to update the state
      loadImageList();
    } catch (err) {
      Toast.fail({
        content: i18next.t('j-dingtalk-web_pages_aiImage_components_ImageItem_OperationFailed'),
        position: 'top',
        duration: 2,
        maskClickable: true,
      });
    }
  };

  // Handle regenerate
  const handleRegenerate = () => {
    onRegenerate(imageInfo);
  };

  // 计算模糊效果 - Calculate blur effect for progressive image generation
  const getBlurValue = (imageIndex: number, progressValue: number): number => {
    const imageProgress = Math.max(0, Math.min(100, (progressValue - imageIndex * 25) * 4));
    return Math.max(0, 20 - (imageProgress / 100) * 20);
  };

  const currentProgress = progress || imageInfo.progress || 0;
  const isGenerating = imageInfo.status === 'pending' || imageInfo.status === 'processing';
  const isFailed = imageInfo.status === 'failed';
  const isCompleted = imageInfo.status === 'completed';

  return (
    <div className={`image-item ${className}`}>
      {/* Image Content */}
      <div className="image-content">
        {isCompleted && (
          <div className="image-container-completed">
            <div className="image-grid">
              {imageInfo.imgUrls?.slice(0, 4).map((url, index) => (
                <OptimizedImage
                  key={`${imageInfo.uuid}-${url}-${index}`}
                  src={url}
                  alt={`Generated image ${index + 1}`}
                  className="image-preview-grid"
                />
              ))}
            </div>
          </div>
        )}

        {isGenerating && (
          <div className="image-generating">
            <div className="generating-overlay">
              <div className="generating-text">
                {i18next.t('j-dingtalk-web_pages_aiImage_components_ImageItem_GeneratingImage')} {Math.round(currentProgress)}%
              </div>
              <CircularProgress progress={currentProgress} />
              <div className="image-grid-generating">
                {[0, 1, 2, 3].map((index) => (
                  <div
                    key={index}
                    className="image-placeholder"
                    style={{
                      filter: `blur(${getBlurValue(index, currentProgress)}px)`,
                      opacity: currentProgress > index * 25 ? 1 : 0.3,
                    }}
                  />
                ))}
              </div>
            </div>
          </div>
        )}

        {isFailed && (
          <div className="image-failed">
            <div className="failed-overlay">
              <div className="failed-text">{i18next.t('j-dingtalk-web_pages_aiImage_components_ImageItem_GenerationFailed')}</div>
              <div className="failed-reason">{imageInfo.errorMsg || i18next.t('j-dingtalk-web_pages_aiImage_components_ImageItem_UnknownError')}</div>
            </div>
          </div>
        )}
      </div>

      {/* Image info */}
      <div className="image-info">
        <div className="image-prompt">{imageInfo.prompt}</div>
        <div className="image-meta">
          <span className="image-size">{imageInfo.size}</span>
          <span className="image-time">
            {new Date(imageInfo.createAt).toLocaleDateString()}
          </span>
        </div>
      </div>

      {/* Action buttons - 只保留点赞、点踩、重新生成三个按钮 */}
      {isCompleted && (
        <div className="image-actions">
          <button
            className={`action-button ${imageInfo.liked ? 'liked' : ''}`}
            onClick={handleLike}
          >
            {imageInfo.liked ? <LikeFilled /> : <LikeOutlined />}
          </button>

          <button
            className={`action-button ${imageInfo.disliked ? 'disliked' : ''}`}
            onClick={handleDislike}
          >
            {imageInfo.disliked ? <DislikeFilled /> : <DislikeOutlined />}
          </button>

          <button
            className="action-button"
            onClick={handleRegenerate}
          >
            <RefreshOutlined />
          </button>
        </div>
      )}
    </div>
  );
};

export default ImageItem;
