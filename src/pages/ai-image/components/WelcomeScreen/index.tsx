import React from 'react';
import { Button } from 'dingtalk-design-mobile';
import './index.less';

interface WelcomeScreenProps {
  hasGenerateBtn: boolean;
  onGetStarted: () => void;
}

const WelcomeScreen: React.FC<WelcomeScreenProps> = ({ hasGenerateBtn, onGetStarted }) => {
  return (
    <div className="ai-image-welcome-screen">
      <div className="welcome-content">
        <div className="logo-section">
          <img
            src="https://img.alicdn.com/imgextra/i4/O1CN01zAgMIH1VJ37xaD35D_!!6000000002631-2-tps-173-194.png"
            alt="logo"
            className="logo-image"
          />
          <h1 className="title">AI画像生成</h1>
          <p className="subtitle">
            让AI帮您创造<br />
            独一无二的<br />
            精美图片
          </p>
        </div>

        {hasGenerateBtn && (
          <Button
            type="primary"
            size="large"
            className="get-started-btn"
            onClick={onGetStarted}
          >
            开始创作
          </Button>
        )}
      </div>
    </div>
  );
};

export default WelcomeScreen;
