import React, { useState, useRef, useEffect } from 'react';
import { CheckOutlined, DropDownArrowDownOutlined } from '@ali/ding-icons';
import './index.less';

interface SizeOption {
  id: string;
  label: string;
  ratio: string;
  width: number;
  height: number;
}

interface SizeSelectorProps {
  value: string;
  onChange: (size: string) => void;
  disabled?: boolean;
}

const SizeSelector: React.FC<SizeSelectorProps> = ({
  value,
  onChange,
  disabled = false,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Size options according to design requirements
  const sizeOptions: SizeOption[] = [
    {
      id: '1:1',
      label: '800x800',
      ratio: '1:1',
      width: 800,
      height: 800,
    },
    {
      id: '3:4',
      label: '750x1000',
      ratio: '3:4',
      width: 750,
      height: 1000,
    },
    {
      id: '16:9',
      label: '1280x720',
      ratio: '16:9',
      width: 1280,
      height: 720,
    },
  ];

  // Get current selected option
  const selectedOption = sizeOptions.find((option) => option.id === value) || sizeOptions[0];

  // Handle dropdown toggle
  const handleToggle = () => {
    if (!disabled) {
      setIsOpen(!isOpen);
    }
  };

  // Handle option selection
  const handleOptionSelect = (optionId: string) => {
    if (!disabled) {
      onChange(optionId);
      setIsOpen(false);
    }
  };

  // Handle click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  return (
    <div className={`size-selector ${disabled ? 'disabled' : ''}`} ref={dropdownRef}>
      {/* Dropdown trigger */}
      <div
        className={`size-selector-trigger ${isOpen ? 'open' : ''}`}
        onClick={handleToggle}
      >
        <span className="size-selector-value">{selectedOption.label}</span>
        <DropDownArrowDownOutlined className="size-selector-arrow" />
      </div>

      {/* Dropdown options */}
      {isOpen && (
        <div className="size-selector-dropdown">
          {sizeOptions.map((option) => (
            <div
              key={option.id}
              className={`size-selector-option ${value === option.id ? 'selected' : ''}`}
              onClick={() => handleOptionSelect(option.id)}
            >
              <span className="option-label">{option.label}</span>
              {value === option.id && (
                <CheckOutlined className="option-check" />
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default SizeSelector;
