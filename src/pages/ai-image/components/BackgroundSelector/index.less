@import (reference) "~dingtalk-theme/dingtalk-x/mob.less";

.background-selector {
  // 横向滚动布局 - Horizontal scrolling layout
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  overflow-x: auto;
  overflow-y: visible; // 允许垂直方向溢出以显示放大效果 - Allow vertical overflow for scale effect
  gap: 8px;
  padding: 4px; // 为放大效果留出空间 - Space for scale effect

  // Windows端滚动优化 - Windows scrolling optimization
  scroll-behavior: smooth; // 平滑滚动 - Smooth scrolling
  overscroll-behavior-x: contain; // 防止过度滚动 - Prevent overscroll

  // 触摸设备滚动优化 - Touch device scrolling optimization
  -webkit-overflow-scrolling: touch; // iOS平滑滚动 - iOS smooth scrolling

  // 完全隐藏滚动条 - Completely hide scrollbar
  // Webkit浏览器 (Chrome, Safari, Edge) - Webkit browsers
  &::-webkit-scrollbar {
    display: none; // 完全隐藏滚动条 - Completely hide scrollbar
  }

  // Firefox浏览器 - Firefox browser
  scrollbar-width: none; // Firefox隐藏滚动条 - Hide scrollbar in Firefox

  // IE浏览器 (兼容性) - IE browser compatibility
  -ms-overflow-style: none; // IE隐藏滚动条 - Hide scrollbar in IE

  .background-option {
    width: 72px;
    height: 72px;
    cursor: pointer;
    transition: all @common_light_motion_duration @common_light_motion_timing_function;
    overflow: visible; // 允许放大效果溢出 - Allow scale effect to overflow
    z-index: 1; // 确保悬停时在其他元素之上 - Ensure it's above other elements when hovered

    // 移动端：防止收缩 - Mobile: prevent shrinking
    @media (max-width: 768px) {
      flex-shrink: 0;
    }

    &:hover:not(.disabled) {
      transform: scale(1.05); // 轻微放大效果 - Slight scale effect
      z-index: 10; // 悬停时提升层级 - Increase z-index on hover
    }

    // 选中状态样式由内部图标处理 - Selected state handled by internal icon

    &.disabled {
      cursor: not-allowed;
      opacity: 0.5;
    }

    .background-preview {
      width: 72px; // 固定尺寸 72px - Fixed size 72px
      height: 72px;
      border: none; // 移除边框 - Remove border
      border-radius: 8px; // 圆角 8px - Border radius 8px
      position: relative;
      overflow: hidden;
      transition: all @common_light_motion_duration @common_light_motion_timing_function;

      .selected-indicator {
        position: absolute;
        top: 6px; // 调整位置 - Adjust position
        right: 6px;
        font-size: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #FF0E53;
        z-index: 1;
        box-shadow: 0 2px 4px rgba(255, 255, 255, 0, 0.2); // 添加阴影增强可见性 - Add shadow for better visibility
      }
    }
  }

  &.disabled {
    .background-option {
      cursor: not-allowed;

      &:hover {
        transform: none;
      }
    }
  }
}
