.image-form {
  min-height: 100vh;
  background-color: #1d1d1d;
  padding: 16px;
  // Add iOS safe area support for bottom padding
  padding-bottom: calc(env(safe-area-inset-bottom) + 16px);

  .form-container {
    margin: 0 auto;
    background: transparent;
    padding: 0;

    .form-section {
      margin-bottom: 12px;

      .form-title {
        display: flex;
        align-items: center;
        margin-bottom: 12px;
        gap: 6px;

        .form-title-text {
          color: rgba(255, 255, 255, 0.9);
          font-size: 16px;
          line-height: 24px;
          font-weight: 600;
        }

        .form-title-info-icon {
          color: rgba(255, 255, 255, 0.4);
          font-size: 20px;
          cursor: pointer;
          transition: color 0.2s ease;

          &:hover {
            color: rgba(255, 255, 255, 0.6);
          }

          &:active {
            transform: scale(0.95);
          }
        }
      }

      .textarea-container {
        position: relative;
        font-size: 0;

        .custom-textarea {
          width: 100%;
          min-height: 132px;
          background: #141414;
          border: 1.5px solid #141414;
          border-radius: 16px;
          padding: 18px 16px;
          color: #ffffff;
          font-size: 16px;
          line-height: 22px;
          resize: none; // Disable manual resize handle on PC
          outline: none;

          &::placeholder {
            color: rgba(255, 255, 255, 0.5);
          }

          &:hover {
            border: 1.5px solid #FF0E53;
          }

          &:active {
            border: 1.5px solid #FF0E53;
            box-shadow: 0px 4px 10px 0px rgba(255, 14, 83, 0.3);
          }
        }

        .char-count {
          position: absolute;
          bottom: 8px;
          right: 12px;
          color: rgba(255, 255, 255, 0.5);
          font-size: 12px;
        }
      }

      .quality-section {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 0;

        .quality-label {
          color: rgba(255, 255, 255, 0.9);
          font-size: 16px;
          line-height: 22px;
          font-weight: 600;
        }

        .quality-toggle-container {
          .quality-toggle {
            position: relative;
            display: flex;
            background: #141414;
            border-radius: 19px;
            padding: 3px;
            width: 150px;
            height: 38px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            overflow: hidden;
            cursor: pointer;
            transition: all 0.2s ease;

            // Add subtle glow effect
            &::before {
              content: '';
              position: absolute;
              top: 0;
              left: 0;
              right: 0;
              bottom: 0;
              background: #141414;
              border-radius: 9px;
              pointer-events: none;
            }

            .quality-slider {
              position: absolute;
              top: 3px;
              left: 3px;
              width: 71px;
              height: 30px;
              background: rgba(255, 255, 255, 0.24);
              border-radius: 17px;
              transition: transform 0.35s cubic-bezier(0.25, 0.46, 0.45, 0.94);
              z-index: 1;

              &.slide-left {
                transform: translateX(0);
              }

              &.slide-right {
                transform: translateX(71px);
              }
            }

            .quality-option {
              position: relative;
              flex: 1;
              background: transparent;
              border: none;
              color: rgba(255, 255, 255, 0.9);
              font-size: 14px;
              cursor: pointer;
              z-index: 2;
              transition: all 0.3s ease;
              border-radius: 17px;
              display: flex;
              align-items: center;
              justify-content: center;
              user-select: none;
              -webkit-tap-highlight-color: transparent;

              &.active {
                color: #ffffff;
                font-weight: 600;
              }

              &:hover:not(.active) {
                color: rgba(255, 255, 255, 0.9);
              }

              &:active {
                transform: scale(0.98);
              }
            }

            // Add ripple effect on touch
            &:active {
              .quality-slider {
                transform: scale(0.98) translateX(var(--slider-position, 0));
              }
            }
          }
        }
      }

      .duration-section {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 0;

        .duration-label {
          color: rgba(255, 255, 255, 0.9);
          font-size: 16px;
          line-height: 22px;
          font-weight: 600;
        }

        .duration-value {
          color: #ffffff;
          font-size: 16px;
          font-weight: 500;
          line-height: 22px;
          padding-right: 2px;
        }
      }
    }

    .error-message {
      background: rgba(255, 77, 79, 0.1);
      border: 1px solid rgba(255, 77, 79, 0.3);
      border-radius: 8px;
      padding: 12px 16px;
      color: #ff4d4f;
      margin-bottom: 24px;
      font-size: 14px;
    }

    .form-actions {
      padding-top: 48px;
      padding-bottom: 24px;

      .generate-btn {
        width: 100%;
        height: 56px;
        line-height: 56px;
        border-radius: 28px;
        font-size: 24px;
        font-weight: 500;
        background: #ffffff;
        border: none;
        color: #141414;
        cursor: pointer;

        &:hover {
          background: rgba(255, 255, 255, 0.9);
          color: #000000;
        }

        &:active {
          background: rgba(255, 255, 255, 0.8);
          color: #000000;
        }

        &.dtm-button-disabled {
          background: #979797;
          color: #666666;
          cursor: not-allowed;

          &:hover {
            background: #979797;
            color: #666666;
          }

          &:active {
            background: #979797;
            color: #666666;
          }
        }
      }
    }
  }

  // Help content styles
  .help-drawer-content {
    padding: 16px;
    overflow-y: auto;

    .help-content-item {
      margin-bottom: 24px;

      &:last-child {
        margin-bottom: 0;
      }

      .help-text {
        color: #333333;
        font-size: 14px;
        line-height: 20px;
        margin: 0;
      }

      .help-content-image {
        width: 100%;
        max-width: 300px;
        height: auto;
        border-radius: 6px;
        margin: 6px 0;
        display: block;
      }

      // Section styles for help content (Mobile drawer)
      .help-section {
        .help-section-title {
          font-size: 16px;
          font-weight: 600;
          color: rgba(255, 255, 255, 0.9);
          margin: 0 0 8px 0;
        }

        .help-section-description {
          font-size: 14px;
          color: rgba(255, 255, 255, 0.9);
          line-height: 22px;
        }

        .help-section-list {
          margin: 0;
          list-style: none;

          .help-section-item {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.9);
            line-height: 22px;
            margin-bottom: 4px;
            position: relative;
          }
        }
      }

      // Examples styles for help content (Mobile drawer)
      .help-examples {
        margin: 16px 0 0;

        .help-examples-header {
          display: flex;
          align-items: center;
          margin-bottom: 12px;

          .help-examples-icon {
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            font-weight: bold;
            margin-right: 8px;

            &.correct {
              color: rgba(85, 255, 201);
            }

            &.incorrect {
              color: #FF0E53;
            }
          }

          .help-examples-title {
            font-size: 16px;
            font-weight: 600;
            line-height: 24px;
            margin: 0;

            &.correct {
              color: rgba(85, 255, 201);
            }

            &.incorrect {
              color: #FF0E53;
            }
          }
        }

        .help-examples-grid {
          display: grid;
          gap: 18px;

          // Default: 2 columns for image upload examples (图片规范)
          grid-template-columns: repeat(2, 1fr);

          // Single column for prompt examples (创意描述参考)
          &.single-column {
            grid-template-columns: 1fr;
          }

          // Responsive design for mobile
          @media (max-width: 480px) {
            gap: 12px;

            // Force single column on very small screens
            &:not(.single-column) {
              grid-template-columns: 1fr;
            }
          }

          .help-example-item {
            text-align: center;

            .help-example-image-placeholder {
              width: 100%;
              background-color: transparent;
              border-radius: 16px;
              display: flex;
              align-items: center;
              justify-content: center;
              margin-bottom: 6px;
              overflow: hidden;

              .placeholder-content {
                font-size: 11px;
                color: rgba(255, 255, 255, 0.5);
              }

              .help-example-image {
                width: 100%;
                height: 100%;
                object-fit: cover;
              }
            }

            .help-example-label-container {
              display: flex;
              align-items: center;
              justify-content: space-between;
              gap: 6px;
              margin-bottom: 8px;
            }

            .help-example-label {
              color: rgba(255, 255, 255, 0.9);
              font-size: 14px;
              font-weight: 500;
              line-height: 20px;
              text-align: left;
              margin: 0;
              flex: 1;
            }

            .help-example-copy-btn {
              background: none;
              border: none;
              padding: 2px;
              font-size: 18px;
              color: rgba(255, 255, 255, 0.9);
              border-radius: 3px;
              display: flex;
              align-items: center;
              justify-content: center;
              transition: all 0.2s ease;
              flex-shrink: 0;
              cursor: pointer;

              svg {
                width: 16px !important;
                height: 16px !important;
              }

              &:hover {
                transform: scale(1.05);
              }

              &:active {
                transform: scale(0.95);
              }

              svg {
                width: 12px;
                height: 12px;
              }
            }
          }
        }
      }
    }
  }
}

// Help popover styles for PC
.form-help-popover {
  &.dtd-popover {
    padding: 0;
    margin: 16px 0;
    max-height: 100vh;
    background-color: #1E1E1F;
    color: #fff;
    border-radius: 32px;
    overflow-y: auto;
  }

  .dtd-popover-inner {
    background: transparent;
    border-radius: 32px;
    padding: 0;
    max-width: 412px;
  }

  .dtd-popover-title {
    padding: 20px 16px 16px 16px;
    font-size: 18px;
    font-weight: 600;
    line-height: 24px;
    color: rgba(255, 255, 255, 0.9);
    border-bottom: 0.5px solid rgba(255, 255, 255, 0.24);
    margin: 0;
  }

  .dtd-popover-inner-content {
    background: transparent;
    color: rgba(255, 255, 255, 0.9);
  }

  .dtd-popover-content {
    padding: 0;
  }

  .help-popover-content {
    padding: 0;
    overflow-y: auto;

    .help-content-item {
      margin-bottom: 24px;

      &:last-child {
        margin-bottom: 0;
      }

      .help-text {
        color: rgba(255, 255, 255, 0.9);
        font-size: 14px;
        line-height: 20px;
        margin: 0;
      }

      .help-content-image {
        width: 100%;
        max-width: 300px;
        height: auto;
        border-radius: 6px;
        margin: 6px 0;
        display: block;
      }

      // Section styles for help content (PC popover)
      .help-section {
        .help-section-title {
          font-size: 16px;
          font-weight: 600;
          color: rgba(255, 255, 255, 0.9);
          margin: 0 0 8px 0;
        }

        .help-section-description {
          font-size: 14px;
          color: rgba(255, 255, 255, 0.9);
          line-height: 22px;
        }

        .help-section-list {
          margin: 0;
          list-style: none;

          .help-section-item {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.9);
            line-height: 22px;
            margin-bottom: 4px;
            position: relative;
          }
        }
      }

      // Examples styles for help content (PC popover)
      .help-examples {
        margin: 16px 0 0;

        .help-examples-header {
          display: flex;
          align-items: center;
          margin-bottom: 12px;

          .help-examples-icon {
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            font-weight: bold;
            margin-right: 8px;

            &.correct {
              color: rgba(85, 255, 201);
            }

            &.incorrect {
              color: #FF0E53;
            }
          }

          .help-examples-title {
            font-size: 16px;
            font-weight: 600;
            line-height: 24px;
            margin: 0;

            &.correct {
              color: rgba(85, 255, 201);
            }

            &.incorrect {
              color: #FF0E53;
            }
          }
        }

        .help-examples-grid {
          display: grid;
          gap: 18px;

          // Default: 2 columns for image upload examples (图片规范)
          grid-template-columns: repeat(2, 1fr);

          // Single column for prompt examples (创意描述参考)
          &.single-column {
            grid-template-columns: 1fr;
          }

          .help-example-item {
            text-align: center;

            .help-example-image-placeholder {
              width: 100%;
              background-color: transparent;
              border-radius: 16px;
              display: flex;
              align-items: center;
              justify-content: center;
              margin-bottom: 6px;
              overflow: hidden;

              .placeholder-content {
                font-size: 11px;
                color: rgba(255, 255, 255, 0.5);
              }

              .help-example-image {
                width: 100%;
                height: 100%;
                object-fit: cover;
              }
            }

            .help-example-label-container {
              display: flex;
              align-items: center;
              justify-content: space-between;
              gap: 6px;
              margin-bottom: 8px;
            }

            .help-example-label {
              color: rgba(255, 255, 255, 0.9);
              font-size: 14px;
              font-weight: 500;
              line-height: 20px;
              text-align: left;
              margin: 0;
              flex: 1;
            }

            .help-example-copy-btn {
              background: none;
              border: none;
              padding: 2px;
              font-size: 18px;
              color: rgba(255, 255, 255, 0.9);
              border-radius: 3px;
              display: flex;
              align-items: center;
              justify-content: center;
              transition: all 0.2s ease;
              flex-shrink: 0;
              cursor: pointer;

              svg {
                width: 16px !important;
                height: 16px !important;
              }

              &:hover {
                transform: scale(1.05);
              }

              &:active {
                transform: scale(0.95);
              }

              svg {
                width: 12px;
                height: 12px;
              }
            }
          }
        }
      }
    }
  }
}
