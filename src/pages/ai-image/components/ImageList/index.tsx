import { i18next } from '@ali/dingtalk-i18n';
import React, { useEffect, useRef, useCallback } from 'react';
import { Button } from 'dingtalk-design-mobile';
import { RefreshOutlined, EditOutlined } from '@ali/ding-icons';
import { isMobileDevice } from '@/utils/jsapi';
import ImageItem from '../ImageItem';
import './index.less';

interface ImageInfo {
  uuid: string;
  imgUrls: string[]; // 包含4张图片的数组 - Array containing 4 images
  originImgUrl?: string; // 仅当生成类型时hd时有效，用于图片像素对比
  type: 'normal' | 'hd'; // 普通图片normal,高清图片：hd
  userRating?: string;
  status: string;
  errorMsg?: string;
  createAt: string;
  finishTime?: string;
  // 前端扩展字段
  prompt?: string;
  negativePrompt?: string;
  size?: string;
  background?: string;
  progress?: number;
  liked?: boolean;
  disliked?: boolean; // 添加点踩状态 - Add dislike state
}

interface ImageListProps {
  imageList: ImageInfo[];
  isLoading: boolean;
  error: string | null;
  onCreateNew: () => void;
  onRegenerate?: (imageInfo: ImageInfo) => void;
  onRefresh?: () => void;
  className?: string;
  progressMap?: Record<string, number>; // Map of image UUID to progress percentage
  hasNextPage?: boolean; // Whether there are more pages to load
  isLoadingMore?: boolean; // Loading state for pagination
  onLoadMore?: () => void; // Function to load more images
  loadImageList: () => void; // Load image list
}

const ImageList: React.FC<ImageListProps> = ({
  imageList,
  isLoading,
  error,
  onCreateNew,
  onRegenerate,
  onRefresh,
  className = '',
  progressMap = {},
  hasNextPage = false,
  isLoadingMore = false,
  onLoadMore,
  loadImageList,
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const scrollTimeoutRef = useRef<number | null>(null);

  // Handle scroll event for infinite loading with throttling
  const handleScrollThrottled = useCallback(() => {
    if (scrollTimeoutRef.current) {
      window.clearTimeout(scrollTimeoutRef.current);
    }

    scrollTimeoutRef.current = window.setTimeout(() => {
      if (!hasNextPage || isLoadingMore || !onLoadMore) {
        return;
      }

      let scrollTop: number;
      let scrollHeight: number;
      let clientHeight: number;

      if (isMobileDevice()) {
        // Mobile: use window scroll
        scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        scrollHeight = document.documentElement.scrollHeight;
        clientHeight = window.innerHeight;
      } else {
        // PC: use right panel scroll (parent container)
        const rightPanel = containerRef.current?.closest('.ai-image-right-panel') as HTMLElement;
        if (!rightPanel) return;

        scrollTop = rightPanel.scrollTop;
        scrollHeight = rightPanel.scrollHeight;
        clientHeight = rightPanel.clientHeight;
      }

      // Trigger load more when scrolled to 80% of the content
      const scrollPercentage = (scrollTop + clientHeight) / scrollHeight;

      if (scrollPercentage >= 0.8) {
        onLoadMore();
      }
    }, 100); // 100ms throttle
  }, [hasNextPage, isLoadingMore, onLoadMore]);

  // Add scroll listener
  useEffect(() => {
    if (isMobileDevice()) {
      // Mobile: listen to window scroll
      window.addEventListener('scroll', handleScrollThrottled);
      return () => {
        window.removeEventListener('scroll', handleScrollThrottled);
      };
    } else {
      // PC: listen to right panel scroll
      const rightPanel = containerRef.current?.closest('.ai-image-right-panel') as HTMLElement;
      if (!rightPanel) return;

      rightPanel.addEventListener('scroll', handleScrollThrottled);
      return () => {
        rightPanel.removeEventListener('scroll', handleScrollThrottled);
      };
    }
  }, [handleScrollThrottled]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (scrollTimeoutRef.current) {
        window.clearTimeout(scrollTimeoutRef.current);
      }
    };
  }, []);

  // Render empty state
  const renderEmptyState = () =>
    (
      <div className="image-list-empty">
        <div className="empty-icon">🖼️</div>
        <div className="empty-title">{i18next.t('j-dingtalk-web_pages_aiImage_components_ImageList_NoImageAvailable')}</div>
        <div className="empty-description">{i18next.t('j-dingtalk-web_pages_aiImage_components_ImageList_StartCreatingYourFirstAi')}

        </div>
        <Button
          type="primary"
          size="large"
          className="create-first-image-btn"
          onClick={onCreateNew}
        >
          {i18next.t('j-dingtalk-web_pages_aiImage_components_ImageList_CreateAnImage')}
        </Button>
      </div>
    );


  // Render error state
  const renderErrorState = () =>
    (
      <div className="image-list-error">
        <div className="error-icon">❌</div>
        <div className="error-title">{i18next.t('j-dingtalk-web_pages_aiImage_components_ImageList_FailedToLoad')}</div>
        <div className="error-description">
          {error || i18next.t('j-dingtalk-web_pages_aiImage_components_ImageList_FailedToObtainTheImage')}
        </div>
        <div className="error-actions">
          {onRefresh &&
          <Button
            type="secondary"
            size="middle"
            onClick={onRefresh}
            className="retry-btn"
          >

            <RefreshOutlined />{i18next.t('j-dingtalk-web_pages_aiImage_components_ImageList_Retry')}

          </Button>
          }
          <Button
            type="primary"
            size="middle"
            onClick={onCreateNew}
            className="create-new-btn"
          >{i18next.t('j-dingtalk-web_pages_aiImage_components_ImageList_CreateANewImage')}


          </Button>
        </div>
      </div>
    );


  // Render loading state
  const renderLoadingState = () =>
    (
      <div className="image-list-loading">
        <div className="loading-spinner" />
        <div className="loading-text">{i18next.t('j-dingtalk-web_pages_aiImage_components_ImageList_LoadingImageList')}</div>
      </div>
    );

  // Render image list content
  const renderImageList = () =>
    (
      <div className="image-list-content">
        {/* Header with create button */}
        {
          isMobileDevice() &&
          <div className="image-list-header">
            <div className="header-title">
              <img className="logo" src="https://img.alicdn.com/imgextra/i4/O1CN01zAgMIH1VJ37xaD35D_!!6000000002631-2-tps-173-194.png" alt="arrow" />
              <span className="image-title">AI Image Generation</span>
            </div>
            <EditOutlined className="create-image-btn" onClick={onCreateNew} />
          </div>

        }

        {/* Image list */}
        <div className="image-list-items" ref={containerRef}>
          {imageList.map((imageInfo) =>
            (<ImageItem
              key={imageInfo.uuid}
              imageInfo={imageInfo}
              onRegenerate={onRegenerate || (() => {})}
              loadImageList={loadImageList}
              className="image-list-item"
              progress={progressMap[imageInfo.uuid] || 0}
            />))}
        </div>

        {/* Load more section */}
        {imageList.length > 0 &&
        <div className="load-more-section">
          {isLoadingMore &&
          <div className="load-more-loading">
            <div className="loading-spinner" />
            <div className="loading-text">{i18next.t('j-dingtalk-web_pages_aiImage_components_ImageList_LoadingMore')}</div>
          </div>
          }
          {!isLoadingMore && !hasNextPage &&
          <div className="load-more-text">已显示全部数据</div>
          }
          {!isLoadingMore && hasNextPage &&
          <div className="load-more-text">{i18next.t('j-dingtalk-web_pages_aiImage_components_ImageList_SlideDownToLoadMore')}

          </div>
          }
        </div>
        }
      </div>
    );


  return (
    <div className={`image-list ${className}`}>
      {isLoading && renderLoadingState()}
      {!isLoading && error && renderErrorState()}
      {!isLoading && !error && imageList.length === 0 && renderEmptyState()}
      {!isLoading && !error && imageList.length > 0 && renderImageList()}
    </div>);
};

export default ImageList;
