export default {
 "j-agent-web_pages_category_components_CategorySelect_NoCategoryDataIsAvailable": "利用可能なカテゴリデータがありません",
 "j-agent-web_pages_category_LevelCategory": "第1階層カテゴリ",
 "j-agent-web_pages_category_ApplyFilterCriteria": "フィルター条件を適用:",
 "j-agent-web_pages_category_components_CategorySelect_LevelCategory": "第1階層カテゴリ",
 "j-agent-web_pages_category_components_CategorySelect_SecondaryCategory": "第2階層カテゴリ",
 "j-agent-web_pages_category_components_CategorySelect_LevelCategory_1": "第3階層カテゴリ",
 "j-agent-web_pages_category_ClosedSuccessfully": "閉じました",
 "j-agent-web_pages_category_FailedToClose": "閉じられませんでした",
 "j-agent-web_pages_comments_CurrentlyScoringIsNotSupported": "現時点では、評価ができません",
 "j-agent-web_pages_commodityDetail_components_CustomerReviews_CommentsDatacommdesc": "コメント（{{dataCommDesc}}）",
 "j-agent-web_pages_commodityDetail_components_CustomerReviews_CustomerCommentFeatures": "お客様レビューの特徴：",
 "j-agent-web_pages_commodityDetail_components_ProductGallery_OpportunityList": "売れ筋",
 "j-agent-web_pages_commodityDetail_components_ProductGallery_SoaringList": "急上昇",
 "j-agent-web_pages_commodityDetail_components_ProductGallery_NewProductList": "注目",
 "j-agent-web_pages_commodityDetail_components_ProductGallery_SalesList": "販売数",
 "j-agent-web_pages_commodityDetail_components_ProductGallery_DailySalesList": "日販売数",
 "j-agent-web_pages_commodityDetail_components_ProductGallery_WeeklySalesList": "週間販売数",
 "j-agent-web_pages_commodityDetail_components_ProductGallery_MonthlySalesList": "月間販売数",
 "j-agent-web_pages_commodityDetail_components_ProductInfo_Comments": "コメント",
 "j-agent-web_utils_util_AYearAgo": "1年前",
 "j-agent-web_utils_util_YearsYearsAgo": "{{years}}年前",
 "j-agent-web_utils_util_AMonthAgo": "1ヶ月前",
 "j-agent-web_utils_util_MonthsMonthsAgo": "{{months}}ヶ月前",
 "j-agent-web_utils_util_ADayAgo": "1日前",
 "j-agent-web_utils_util_DaysDaysAgo": "{{days}}日前",
 "j-agent-web_utils_util_AnHourAgo": "1時間前",
 "j-agent-web_utils_util_HoursHoursAgo": "{{hours}}時間前",
 "j-agent-web_utils_util_OneMinuteAgo": "1分前",
 "j-agent-web_utils_util_MinutesMinutesAgo": "{{minutes}}分前",
 "j-agent-web_utils_util_JustNow": "たった今",
 "j-agent-web_utils_util_SecondsSecondsAgo": "{{seconds}}秒前",
 "j-agent-web_pages_commodityDetail_components_ProductPage_SorryThereIsNoProduct": "申し訳ありませんが、商品データがございません",
  "j-agent-web_pages_category_SubscriptionSuccessful": "登録完了",
  "j-agent-web_pages_agentForm_components_FileUploader_FilenameUploadFailed": "{{fileName}} アップロード失敗",
  "j-agent-web_pages_agentForm_components_FileUploader_FilenameUploadedSuccessfully": "{{fileName}} アップロード成功",
  "j-agent-web_pages_agentForm_components_FileUploader_UploadFailed": "アップロード失敗:",
  "j-agent-web_pages_agentForm_components_FileUploader_UploadFiles": "ファイルをアップロード",
  "j-agent-web_pages_agentForm_components_FileUploader_SupportedFormatPhtASingle": "対応フォーマット：.pht、1ファイル最大200MB、最大ファイル数",
  "j-agent-web_pages_agentForm_components_FileUploader_Files": "{0}個のファイル",
  "j-agent-web_pages_agentForm_components_FileUploader_PreviewImage": "画像プレビュー",
  "j-agent-web_pages_agentForm_components_FormComponent_ModelId": "モデルID",
  "j-agent-web_pages_agentForm_components_FormComponent_Copy": "コピー",
  "j-agent-web_pages_agentForm_components_FormComponent_CopiedSuccessfully": "コピー完了",
  "j-agent-web_pages_agentForm_components_FormComponent_UpdatedSuccessfully": "更新成功",
  "j-agent-web_pages_agentForm_components_FormComponent_ModelUploadFailedErr": "モデルアップロード失敗{{err}}",
  "j-agent-web_pages_agentForm_components_FormComponent_FormSubmissionError": "フォーム送信エラー:",
  "j-agent-web_pages_agentForm_components_FormComponent_FormSubmissionFailedPleaseTry": "フォーム送信エラー、再試行してください！",
  "j-agent-web_pages_agentForm_components_FormComponent_TheFormHasBeenReset": "フォームがリセットされました",
  "j-agent-web_pages_agentForm_components_FormComponent_SelectAModelToUpdate": "更新するモデルを選択してください",
  "j-agent-web_pages_agentForm_components_FormComponent_TheModelToBeUpdated": "更新するモデルが選択されました",
  "j-agent-web_pages_agentForm_components_FormComponent_ModelName": "モデル名",
  "j-agent-web_pages_agentForm_components_FormComponent_EnterAModelName": "モデル名を入力してください",
  "j-agent-web_pages_agentForm_components_FormComponent_UploadModelFiles": "モデルファイルをアップロード",
  "j-agent-web_pages_agentForm_components_FormComponent_PromptEditingSupportsMarkdown": "プロンプト編集はマークダウンに対応",
  "j-agent-web_pages_agentForm_components_FormComponent_EnterPrompt": "プロンプトを入力してください",
  "j-agent-web_pages_agentForm_components_FormComponent_ModelUpdate": "モデル更新",
  "j-agent-web_pages_agentForm_components_FormComponent_ModelUpload": "モデルアップロード",
  "j-agent-web_pages_agentForm_components_FormComponent_Reset": "リセット",
  "j-agent-web_pages_agentForm_components_MarkdownEditor_Bold": "太字",
  "j-agent-web_pages_agentForm_components_MarkdownEditor_Italic": "斜体",
  "j-agent-web_pages_agentForm_components_MarkdownEditor_OrderedList": "番号付きリスト",
  "j-agent-web_pages_agentForm_components_MarkdownEditor_UnorderedList": "箇条書きリスト",
  "j-agent-web_pages_agentForm_components_MarkdownEditor_Link": "リンク",
  "j-agent-web_pages_agentForm_components_MarkdownEditor_Edit": "編集",
  "j-agent-web_pages_agentForm_components_MarkdownEditor_MarkdownFormatEditingIsSupported": "マークダウン記法をサポートしています。テキスト、リスト、リンクなどが入力可能です",
  "j-agent-web_pages_agentForm_components_MarkdownEditor_Preview": "プレビュー",
  "j-agent-web_pages_agentForm_components_MarkdownEditor_NoContentPreview": "プレピューなし",
  "j-agent-web_pages_agentForm_AddModel": "モデルを追加",
  "j-agent-web_pages_agentForm_UpdateModelPrompt": "モデルプロンプトを更新",
  "j-agent-web_pages_agentForm_services_ossService_FailedToObtainOssConfiguration": "OSS設定の取得に失敗しました:",
  "j-agent-web_pages_agentForm_services_ossService_FailedToObtainUploadCredentials": "アップロード認証情報の取得に失敗しました",
  "j-agent-web_pages_agentForm_services_ossService_FailedToUploadTheFile": "ファイルのアップロードに失敗しました:",
  "j-agent-web_pages_cardTips_ThePlatformSoldTheMost": "当プラットフォームの全カテゴリー「または、ご登録のカテゴリー」における、昨日付の販売数ランキング1位の商品です；",
  "j-agent-web_pages_cardTips_ThePlatformSoldTheMostDaily": "当プラットフォームの全カテゴリー「または、ご登録のカテゴリー」における、昨日付の販売数ランキング1位の商品です；",
  "j-agent-web_pages_cardTips_ThePlatformSoldTheMostWeekly": "当プラットフォームの全カテゴリー「または、ご登録のカテゴリー」における、今週の販売数ランキング1位の商品です；",
  "j-agent-web_pages_cardTips_ThePlatformSoldTheMostMonthly": "当プラットフォームの全カテゴリー「または、ご登録のカテゴリー」における、今月の販売数ランキング1位の商品です；",
  "j-agent-web_pages_cardTips_NewProductsWithTheLargest": "当プラットフォームの全カテゴリー「または、ご登録のカテゴリー」における、直近30日間に登場した新商品のうち、一番「いいね！」された新製品です；",
  "j-agent-web_pages_cardTips_ThePlatformYesterdaySoldThe": "当プラットフォームの全カテゴリー「または、ご登録のカテゴリー」における、昨日の売上伸び率が前日比で最も高かった商品です；",
  "j-agent-web_pages_cardTips_ThePlatformYesterdaySoldAll": "当プラットフォームの全カテゴリー「または、ご登録のカテゴリー」における、直近30日間にランキング入り日数が最も長い商品です。",
  "j-agent-web_pages_cardTips_DataDescription": "データ説明",
  "j-agent-web_pages_commodityDetail_components_ProductInfo_StoreNameProductstorename": "ショップ：",
  "j-agent-web_pages_productCuration_components_ProductList_Rating": "評価：",
  "j-agent-web_pages_productCuration_ProductSelectionCenter": "商品選定センター",
  "j-agent-web_pages_productCuration_UnableToObtainBizid": "bizIdが取得できません",
  "j-agent-web_pages_productCuration_FailedToObtainProductList": "商品リストの取得に失敗しました",
  "j-agent-web_pages_productCuration_FailedToSubmitTheProduct": "商品の提出に失敗しました",
  "j-agent-web_pages_productCuration_LoadingGoods": "商品読み込み中...",
  "j-agent-web_pages_productCuration_ProductslengthItemsInTotalSelectedcount": "合計 {{productsLength}} 個商品、{{selectedCount}} 個商品を選択しました",
  "j-agent-web_pages_productCuration_DeselectAll": "全選択解除",
  "j-agent-web_pages_productCuration_SelectAllCurrentPage": "ページを全選択",
  "j-agent-web_pages_productCuration_NoProductDataAvailable": "商品データがありません",
  "j-agent-web_pages_productCuration_SubmitSelectionSelectedcount": "選定商品を提出({{selectedCount}})",
  "j-agent-web_pages_productOrigin_components_ProductPage_ProductSourcing": "1688の類似商品",
  "j-agent-web_pages_productOrigin_components_ProductPage_FailedToObtainData": "データの取得に失敗しました",
  "j-agent-web_pages_productOrigin_components_ProductPage_ZozoGrid": "ZOZOTOWNでの価格",
  "j-agent-web_pages_productOrigin_components_ProductPage_AmazonGrid": "Amazonでの価格",
  "j-agent-web_pages_productOrigin_components_ProductPage_RednoteGrid": "参考価格",
  "j-agent-web_pages_productOrigin_components_ProductPage_TiktokGrid": "TikTokでの価格",
  "j-agent-web_pages_productOrigin_components_ProductPage_TaobaoGrid": "Taobaoでの価格",
  "j-agent-web_pages_productOrigin_components_ProductPage_AlibabaGrid": "1688Japanでの価格",
  "j-agent-web_pages_productOrigin_components_ProductPage_RakutenGrid": "楽天での価格",
  "j-agent-web_pages_productOrigin_components_ProductPage_ManualGrid": "価格",
  "j-agent-web_pages_productOrigin_components_ProductPage_Details": "詳細",
  "j-agent-web_pages_productOrigin_components_ProductPage_Favorites": "お気に入り登録数",
  "j-agent-web_pages_productOrigin_components_ProductPage_ProductdatafavnumPeople": "{{productDataFavNum}} 人",
  "j-agent-web_pages_productOrigin_components_ProductPage_NoCollection": "お気に入りなし",
  "j-agent-web_pages_productOrigin_components_ProductPage_List": "ランキング",
  "j-agent-web_pages_productOrigin_components_ProductPage_ProductRating": "商品評価",
  "j-agent-web_pages_productOrigin_components_ProductPage_NoScore": "評価なし",
  "j-agent-web_pages_productOrigin_components_ProductPage_SimilarProducts": "1688類似商品",
  "j-agent-web_pages_category_components_CategorySelect_AllCategories": "すべてのカテゴリ",
  "j-agent-web_pages_commodityDetail_components_ProductInfo_FindSimilarity": "類似商品を探す",
  "j-agent-web_pages_productOriginV2_components_ProductPage_TodaySSalesVolume": "本日販売数",
  "j-agent-web_pages_productOriginV2_components_ProductPage_NumberOfLikes": "いいね数",
  "j-agent-web_pages_productOriginV2_components_ProductPage_DailyGrowth": "日間成長率",
  "j-agent-web_pages_productOriginV2_components_ProductPage_NumberOfDaysOnThe": "ランクイン入り日数",
  "j-agent-web_pages_productOriginV2_components_ProductPage_SalesVolume": "販売数",
  "j-agent-web_pages_productOriginV2_components_ProductPage_MonthlySales": "直近90日間の販売数",
  "j-agent-web_pages_productOriginV2_components_ProductPage_RepurchaseRate": "リピート率",
  "j-agent-web_pages_productOriginV2_components_ProductPage_ProductEvaluation": "商品評価",
  "j-agent-web_pages_productOriginV2_components_ProductPage_ListSource": "ランキング出典",
  "j-agent-web_pages_productOriginV2_components_ProductPage_NoListInformation": "ランキング情報なし",
  "j-agent-web_pages_productOriginV2_components_ProductPage_FactoryPrice": "工場直売価格",
  "j-agent-web_pages_productCuration_components_ProductList_ViewProductDetails": "商品詳細を見る",
  "j-agent-web_pages_productOrigin_components_ProductPage_ShareContent": "1688japanを探しているあなたへ！こっち来て！中国の豊富な商品を、日本へ直送。圧倒的な品揃えと安さを体験してください！",
  "j-agent-web_pages_productCuration_SuccessfullySubmitted": "「{{successItemListLen}}件の商品のFeedsへの追加が完了しました。なお、{{failItemListLen}}件は失敗しました。」",
  "j-dingtalk-web_components_ImageSwiper_ProductPortrait": "商品画像",
  "j-dingtalk-web_components_OptimizedImage_PortraitFailure": "画像の読み込みに失敗しました",
  "j-dingtalk-web_pages_commodityDetail_components_ProductPage_ForProductInformationSee": "商品が見つかりません",
  "j-dingtalk-web_pages_productOriginV2_components_ProductPage_Pieces": "件",
  "j-dingtalk-web_pages_productOriginV2_components_ProductPage_ProductPortrait": "商品画像",
  "j-dingtalk-web_pages_productOriginV2_components_ProductPage_NoMore": "以上です",
  "j-dingtalk-web_pages_category_FailedToObtainCategoryData": "カテゴリデータの取得に失敗しました",
  "j-dingtalk-web_pages_category_SelectASecondaryCategory": "第2階層カテゴリを選択してください",
  "j-dingtalk-web_pages_commodityDetail_components_ProductPage_MoreProducts": "もっと見る	",
  "j-dingtalk-web_pages_category_CategorySelectionSucceeded": "登録完了",
  "j-dingtalk-web_pages_category_CategorySelectionFailed": "登録失敗、再試行してください",
  "j-agent-web_pages_commodityDetail_components_CustomerReviews_MoreDetails": "詳細を見る",
  "j-dingtalk-web_pages_commodityDetail_components_ProductInfo_CommentDetails": "コメント詳細",
  "j-agent-web_pages_commodityDetail_components_CustomerReviews_CustomerCommentCount": "{{commentCount}}件のグローバル評価",
  "j-dingtalk-web_pages_commodityDetail_components_AmazonComments_StarRating": "{{rating}}つ星",
  "j-dingtalk-web_pages_commodityDetail_components_AmazonComments_ScoreFullScore": "{{score}}点（5点満点）",
  "j-dingtalk-web_pages_commodityDetail_components_ProductInfo_CustomerFeedback": "お客様のご意見",
  "j-dingtalk-web_pages_commodityDetail_components_ProductInfo_GeneratedByAiBasedOn": "お客様からご提供いただいた内容に基づき、AIが生成しました",
  "j-agent-web_pages_productOrigin_components_ProductPage_Price": "価格",
  "j-dingtalk-web_pages_premiumStore_StoreDetails": "店舗詳細",
  "j-dingtalk-web_pages_premiumStore_ComprehensiveEvaluation": "総合評価：",
  "j-dingtalk-web_pages_premiumStore_ProductQuality": "商品品質：",
  "j-dingtalk-web_pages_premiumStore_DeliverySpeed": "発送速度：",
  "j-dingtalk-web_pages_premiumStore_ConsultingServices": "サービス：",
  "j-dingtalk-web_pages_premiumStore_SimilarProducts": "類似商品",
  "j-dingtalk-web_pages_topMerchant_GoodNumberDetails": "人気アカウント詳細",
  "j-dingtalk-web_pages_topMerchant_FailedToObtainData": "データの取得に失敗しました",
  "j-dingtalk-web_pages_topMerchant_Fans": "ファン",
  "j-dingtalk-web_pages_topMerchant_NumberOfInteractions": "インタラクション数",
  "j-dingtalk-web_pages_topMerchant_DataOverview": "データ概要",
  "j-dingtalk-web_pages_topMerchant_NumberOfLikesReceived": "いいね数",
  "j-dingtalk-web_pages_topMerchant_NumberOfNotes": "ノート数",
  "j-dingtalk-web_pages_topMerchant_NumberOfCooperativeBrands": "ブランドコラボ数",
  "j-dingtalk-web_pages_topMerchant_Mockmerchantinfobrandcount": "{{merchantInfoGoodsNum}}個",
  "j-dingtalk-web_pages_topMerchant_NumberOfConcerns": "フォロワー数",
  "j-dingtalk-web_pages_topMerchant_StyleOverview": "スタイル概要",
  "j-dingtalk-web_pages_topMerchant_HighestInteraction": "インタラクション数1位",
  "j-dingtalk-web_pages_topMerchant_RecentlyReleased": "最近の投稿",
  "j-dingtalk-web_pages_topMerchant_TheGoodsMentionedInThis": "このノートで紹介された商品",
  "j-dingtalk-web_pages_topMerchant_SimilarProducts": "類似商品",
  "j-dingtalk-web_pages_topMerchant_ProductsSoldByTa": "TAが販売している商品",
  "j-dingtalk-web_pages_topMerchant_HighestSalesVolume": "販売数1位",
  "j-dingtalk-web_pages_topMerchant_SimilarProducts_1": "類似商品",
  "j-dingtalk-web_components_ImageSwiper_LookingForSimilarProducts": "類似商品を探す",
  "j-agent-web_pages_productOrigin_components_ProductPage_CopiedToClipboard": "productId をコピーしました",
  "j-dingtalk-web_pages_category_CategorySelect_DailyNewspaper": "日次報告",
  "j-dingtalk-web_pages_category_CategorySelect_WeeklyNewspaper": "週次報告",
  "j-dingtalk-web_pages_category_CategorySelect_MonthlyReport": "月次報告",
  "j-dingtalk-web_pages_category_CategorySelect_EveryWorkingDay": "毎平日",
  "j-dingtalk-web_pages_category_CategorySelect_EveryDay": "毎日",
  "j-dingtalk-web_pages_category_CategorySelect_EveryFriday": "毎週金曜日",
  "j-dingtalk-web_pages_category_CategorySelect_ThOfEachMonth": "毎月1日",
  "j-dingtalk-web_pages_category_CategorySelect_SubscriptionSettings": "登録設定",
  "j-dingtalk-web_pages_category_CategorySelect_SelectDate": "日付を選択",
  "j-dingtalk-web_pages_category_CategorySelect_SelectTime": "時間を選択",
  "j-dingtalk-web_pages_category_CategorySelect_Cancel": "キャンセル",
  "j-dingtalk-web_pages_category_CategorySelect_Confirm": "確認",
  "j-dingtalk-web_pages_category_Comprehensive": "総合",
  "j-dingtalk-web_pages_category_PleaseSelectALevelCategory": "第1階層カテゴリを選択してください",
  "j-dingtalk-web_pages_category_SelectALevelCategory": "第3階層カテゴリを選択してください",
  "j-dingtalk-web_components_Loading_Loading": "読み込み中",
  "j-dingtalk-web_pages_category_CategorySelect_Loading": "読み込み中",
  "j-dingtalk-web_pages_category_CategorySwitchingFailedPleaseTry": "カテゴリ切り替えに失敗しました。再試行してください。",
  "j-dingtalk-web_pages_premiumStore_MonthlySalesProductmonthsolddisplayPieces": "月間販売数{{productMonthSoldDisplay}}件",
  "j-dingtalk-web_pages_premiumStore_FailedToObtainStoreData": "店舗データの読み込みに失敗しました~",
  "j-dingtalk-web_components_SwipeableProductCard_NotInterested": "興味ない",
  "j-dingtalk-web_components_SwipeableProductCard_Interested": "興味ある",
  "j-dingtalk-web_components_SwipeableProductCard_Approve": "承認",
  "j-dingtalk-web_components_SwipeableProductCard_Reject": "拒否",
  "j-dingtalk-web_components_SwipeableProductList_NoProductDataAvailable": "該当する商品はありません",
  "j-dingtalk-web_components_SwipeableProductList_SlideLeftAndRightTo": "← 左右スワイプで商品を切り替える →",
  "j-dingtalk-web_components_SwipeableProductList_IMNotInterestedIn": "↑ 上にスワイプで興味ない、↓ 下にスワイプで興味ある",
  "j-dingtalk-web_pages_productOriginV2_components_ProductPage_ReportFakeAndInferiorProducts": "偽ブランド品を報告",
  "j-dingtalk-web_pages_productOriginV2_components_ProductPage_ReportThatThisProductIs": "偽ブランド品だと報告します",
  "j-dingtalk-web_pages_productOriginV2_components_ProductPage_NotYet": "キャンセル",
  "j-dingtalk-web_pages_productOriginV2_components_ProductPage_ConfirmReport": "報告",
  "j-dingtalk-web_pages_productOriginV2_components_ProductPage_TheParameterIsIncorrectAnd": "パラメータが正しくないため",
  "j-dingtalk-web_pages_productOriginV2_components_ProductPage_ReportSuccessful": "報告に成功しました",
  "j-dingtalk-web_pages_productOriginV2_components_ProductPage_ReportFailedPleaseTryAgain": "報告に失敗しました。再試行してください",
  "j-dingtalk-web_pages_productOriginV2_components_ProductPage_UnknownError": "不明なエラー",
  "j-dingtalk-web_pages_productOriginV2_components_ProductPage_AuditSuccess": "審査{{status}}",
  "j-dingtalk-web_pages_productOriginV2_components_ProductPage_AuditFailed": "審査に失敗しました。後ほどもう一度お試しください",
  "j-dingtalk-web_pages_sourcing_components_Page_MarkedAsInterested": "興味あるとしてマークしました",
  "j-dingtalk-web_pages_sourcing_components_Page_MarkedAsNotInterested": "興味ないとしてマークしました",
  "j-dingtalk-web_pages_sourcing_components_Page_AuditSuccess": "審査{{status}}が成功しました",
  "j-dingtalk-web_pages_sourcing_components_Page_AuditFailed": "審査に失敗しました。後ほどもう一度お試しください",
  "j-dingtalk-web_pages_sourcing_components_Page_SimilarSources": "1688の類似商品",
  "j-dingtalk-web_pages_topMerchant_Favorites": "お気に入り登録数",
  "j-dingtalk-web_pages_productOriginV2_components_ProductPage_Pass": "合格",
  "j-dingtalk-web_pages_productOriginV2_components_ProductPage_NotPassed": "不合格",
  "j-dingtalk-web_utils_util_NumberUnitTenThousand": "万",
  "j-dingtalk-web_utils_util_NumberUnitHundredMillion": "億",
  "j-dingtalk-web_pages_topMerchant_LoadMore": "もっと見る",
  "j-dingtalk-web_pages_aiVideo_components_ImageUploader_UseThisFeatureInThe": "この機能を7Dingでご利用ください",
  "j-dingtalk-web_pages_aiVideo_components_ImageUploader_InitializingPleaseTryAgainLater": "初期化中です。しばらくしてからもう一度お試しください",
  "j-dingtalk-web_pages_aiVideo_components_ImageUploader_ImageUploadedSuccessfully": "画像のアップロードが完了しました",
  "j-dingtalk-web_pages_aiVideo_components_ImageUploader_ImageUploadFailed": "画像のアップロードに失敗しました",
  "j-dingtalk-web_pages_aiVideo_components_ImageUploader_ImageUploadFailedPleaseTry": "画像のアップロードに失敗しました。もう一度お試しください",
  "j-dingtalk-web_pages_aiVideo_components_ImageUploader_ClickUploadImage": "クリックして画像をアップロード",
  "j-dingtalk-web_pages_aiVideo_components_ImageUploader_Uploading": "アップロード中...",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_ImageSpecification": "画像仕様",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_MaterialType": "素材タイプ：",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_TheElementsAreSimpleThe": "1、要素がシンプルで、光が十分で、商品が鮮明で、輪郭がはっきりしている画像",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_ItIsRecommendedToUpload": "2、モデルが商品を着用・使用している画像（バストアップやクローズアップが効果的です）を推奨します",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_Size": "サイズ仕様：",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_SupportsJpgPngFormatWith": "1、JPG/PNG形式、5MB以下のファイルをサポート",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_TheShortSideIsNot": "2、短辺は300PX以上、アスペクト比は5:2〜2:5の範囲内",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_CorrectExample": "良い例",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_HandheldProductWithClearText": "商品を手で持ち、文字が鮮明",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_TheProductIsClearAnd": "商品が鮮明で、輪郭がはっきりしている",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_ModelHalfLengthDisplayClothing": "モデルの着用例（上半身）",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_ModelTrialProducts": "モデルが商品を試用",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_ErrorExample": "悪い例",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_StitchingPicture": "コラージュ画像",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_UnclearSubject": "被写体がぼやける",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_CreativeDescriptionReference": "プロンプトの参考例",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_DescribeThePictureAndAction": "画像に合わせて、生成したいシーンや動きを説明してください。「被写体 + 動作」の形式（例：「モデルが微笑みながら前に歩く」）を推奨します。",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_ReferenceExample": "参考例",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_MenSlowlyPickUpThe": "男性がゆっくりとワイングラスを持ち上げる",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_PromptMenSlowlyPickUp": "プロンプト：男性がゆっくりとワイングラスを持ち上げる",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_TheLensSlowlyRotatesTo": "カメラが右側へゆっくり回り込み、ゲーム機本体を展示",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_PromptTheLensSlowlyRotates": "プロンプト：カメラが右側へゆっくり回り込み、ゲーム機本体を展示",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_ModelPosingShowingYogaClothes": "モデルがポーズを取り、ヨガウェアを展示",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_PromptModelPosingShowingYoga": "プロンプト：モデルがポーズを取り、ヨガウェアを展示",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_ContentYouDonTWant": "除外したい要素（任意入力）",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_CopiedToCreativeDescription": "プロンプトにコピーしました",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_CopyToCreativeDescription": "プロンプトにコピー",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_PleaseUploadAnImage": "画像をアップロードしてください",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_PleaseEnterAForwardDescription": "生成したい内容を入力してください",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_Hd": "高画質",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_StandardClear": "標準画質",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_FirstFrameVideoPicture": "動画の最初フレーム画像",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_YourCreativeDescription": "生成したい内容",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_InputSuchAsModelDisplay": "モデルによる商品紹介が行われています",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_InputSuchAsDistortionDistortion": "歪み、歪曲、変形、低品質などの問題があります",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_VideoDefinition": "動画の画質",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_VideoDuration": "動画の長さ",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_Generating": "生成中...",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_GenerateNow": "今すぐ生成",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_HelpInformation": "ヘルプ情報",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_Generating": "生成中...",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_ItIsExpectedToBe": "完了まで1〜3分かかる見込みです。この間、画面を閉じても構いません",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_Hd": "高画質",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_StandardClear": "標準画質",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_PleaseClickTheFullScreen": "動画右下の全画面表示ボタンをクリックしてください",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_ThePraiseIsCanceled": "いいねを取り消しました",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_ThumbsUpSuccessfully": "いいねしました",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_TheOperationFailedPleaseTry": "操作に失敗しました。もう一度お試しください",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_TheCancellationPointIsStepped": "低評価を取り消しました",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_ClickSuccessfully": "低評価をつけました",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_GifAlreadyExistsStartDownloading": "GIFが既に存在します。ダウンロードを開始します...",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_GifGenerationCompleted": "GIF生成が完了しました！",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_GifGenerationFailedToStart": "GIF生成の開始に失敗しました",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_GifGenerationHasStartedPlease": "GIF生成を開始しました。しばらくお待ちください...",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_GifGenerationFailedPleaseTry": "GIF生成に失敗しました。もう一度お試しください",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_GifGenerationFailed": "GIF生成に失敗しました",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_GifGenerationTimeout": "GIF生成がタイムアウトしました",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_UnknownStatusResultstatus": "不明なステータス：{{resultStatus}}",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_RegeneratingVideo": "動画を再生成中...",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_RegenerationHasStartedPleaseWait": "再生成を開始しました。しばらくお待ちください...",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_RegenerationFailed": "再生成に失敗しました",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_RegenerationFailedPleaseTryAgain": "再生成に失敗しました。もう一度お試しください",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_ConfirmDeletion": "削除を確認",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_AreYouSureYouWant": "この動画を削除してもよろしいですか？",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_Cancel": "キャンセル",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_Delete": "削除",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_DeletedSuccessfully": "削除が完了しました",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_FailedToDeletePleaseTry": "削除に失敗しました。もう一度お試しください",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_WaitingForGeneration": "生成待ち...",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_FailedToGenerate": "生成に失敗しました",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_DownloadGif": "GIFをダウンロード",
  "j-dingtalk-web_pages_aiVideo_components_VideoList_NoVideoAvailable": "動画がありません",
  "j-dingtalk-web_pages_aiVideo_components_VideoList_StartCreatingYourFirstAi": "最初のAI動画を作成してみましょう！",
  "j-dingtalk-web_pages_aiVideo_components_VideoList_CreateAVideo": "+ 動画を作成",
  "j-dingtalk-web_pages_aiVideo_components_VideoList_FailedToLoad": "読み込みに失敗しました",
  "j-dingtalk-web_pages_aiVideo_components_VideoList_FailedToObtainTheVideo": "動画リストの取得に失敗しました。もう一度お試しください",
  "j-dingtalk-web_pages_aiVideo_components_VideoList_Retry": "再試行",
  "j-dingtalk-web_pages_aiVideo_components_VideoList_CreateANewVideo": "+ 新しい動画を作成",
  "j-dingtalk-web_pages_aiVideo_components_VideoList_LoadingVideoList": "動画リストを読み込み中...",
  "j-dingtalk-web_pages_aiVideo_components_VideoList_LoadingMore": "さらに読み込み中...",
  "j-dingtalk-web_pages_aiVideo_components_VideoList_AllVideosAreDisplayed": "すべての動画を表示しました",
  "j-dingtalk-web_pages_aiVideo_components_VideoList_SlideDownToLoadMore": "下にスライドしてさらに読み込む",
  "j-dingtalk-web_pages_aiVideo_VideoGenerationCompleted": "動画生成が完了しました！",
  "j-dingtalk-web_pages_aiVideo_VideoGenerationFailed": "動画生成に失敗しました",
  "j-dingtalk-web_pages_aiVideo_VideoGenerationFailedPleaseTry": "動画生成に失敗しました。もう一度お試しください",
  "j-dingtalk-web_pages_aiVideo_VideoGenerationTimedOutPlease": "動画生成がタイムアウトしました。もう一度お試しください",
  "j-dingtalk-web_pages_aiVideo_UnknownStatusResultstatus": "不明なステータス：{{resultStatus}}",
  "j-dingtalk-web_pages_aiVideo_AnUnknownErrorOccurredWhile": "動画生成中に未知のエラーが発生しました。もう一度お試しください",
  "j-dingtalk-web_pages_aiVideo_QueryStatusFailed": "ステータス照会に失敗しました",
  "j-dingtalk-web_pages_aiVideo_FailedToQueryTheVideo": "動画生成のステータス照会に失敗しました。もう一度お試しください",
  "j-dingtalk-web_pages_aiVideo_FailedToObtainTheVideo": "動画リストの取得に失敗しました",
  "j-dingtalk-web_pages_aiVideo_PleaseUploadAnImage": "画像をアップロードしてください",
  "j-dingtalk-web_pages_aiVideo_PleaseEnterAForwardDescription": "生成したい内容を入力してください",
  "j-dingtalk-web_pages_aiVideo_FailedToGenerate": "生成に失敗しました",
  "j-dingtalk-web_pages_aiVideo_FailedToGeneratePleaseTry": "生成に失敗しました。もう一度お試しください",
  "j-dingtalk-web_pages_aiVideo_Loading": "読み込み中...",
  "j-dingtalk-web_pages_premiumStore_FailedToLoadStoreData": "店舗データの読み込みに失敗しました~",
  "j-dingtalk-web_utils_download_File": "ファイル",
  "j-dingtalk-web_utils_download_Video": "動画",
  "j-dingtalk-web_utils_download_DownloadingFiletype": "{{fileType}}をダウンロード中...",
  "j-dingtalk-web_utils_download_FiletypeDownloadedSuccessfully": "{{fileType}}のダウンロードが完了しました",
  "j-dingtalk-web_utils_download_FiletypeFailedToDownloadPlease": "{{fileType}}のダウンロードに失敗しました。もう一度お試しください",
  "j-dingtalk-web_utils_download_DownloadIsNotSupportedIn": "現在の環境はダウンロード機能に対応していません。",
  "j-dingtalk-web_utils_download_StartDownloadingFiletype": "{{fileType}}のダウンロードを開始",
  "j-dingtalk-web_utils_download_FailedToDownloadFiletypePlease": "{{fileType}}のダウンロードに失敗しました。もう一度お試しください",
  "j-dingtalk-web_utils_download_FiletypeIsOpenInThe": "{{fileType}}がブラウザで開かれました。手動で保存してください",
  "j-dingtalk-web_utils_download_FiletypeIsOpenInA": "{{fileType}}が新しいウィンドウで開かれました。手動で保存してください",
  "j-dingtalk-web_utils_download_SaveAs": "名前を付けて保存",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_ConvertingToGif": "GIFに変換",
  "j-dingtalk-web_pages_premiumStore_NumberOfFans": "フォロワー数",
  "j-dingtalk-web_pages_premiumStore_PositiveRate": "高評価率",
  "j-dingtalk-web_pages_topMerchant_NumberOfProducts": "商品数",
  "j-dingtalk-web_pages_topMerchant_ProductsMentionedInThisVideo": "この動画で紹介された商品",
 "j-dingtalk-web_components_InfiniteList_NoDataAvailable": "データがありません",
 "j-dingtalk-web_components_InfiniteList_NetworkErrorPleaseTryAgain": "ネットワークエラー、後でもう一度お試しください",
 "j-dingtalk-web_pages_my-comment_components_CommentCard_Today": "今日",
 "j-dingtalk-web_pages_my-comment_components_CommentCard_Yesterday": "昨日",
 "j-dingtalk-web_pages_my-comment_components_CommentCard_DiffdaysDaysAgo": "{{diffDays}}日前",
 "j-dingtalk-web_pages_my-comment_components_CommentCard_MMonthDDay": "M月D日",
 "j-dingtalk-web_pages_my-comment_MyComments": "私のコメント",
 "j-dingtalk-web_pages_my-comment_NoCommentRecords": "コメント記録がありません",
 "j-dingtalk-web_pages_my-comment_NetworkErrorPleaseTryAgain": "ネットワークエラー、後でもう一度お試しください",
 "j-dingtalk-web_pages_my-favorites_components_ProductView_NoItemsForCollection": "お気に入りの商品がありません",
 "j-dingtalk-web_pages_my-favorites_components_ProductView_NetworkErrorPleaseTryAgain": "ネットワークエラー、後でもう一度お試しください",
 "j-dingtalk-web_pages_my-favorites_components_StoreView_Contribution": "投稿",
 "j-dingtalk-web_pages_my-favorites_components_StoreView_NoStoreForCollection": "お気に入りのショップがありません",
 "j-dingtalk-web_pages_my-favorites_components_StoreView_NetworkErrorPleaseTryAgain": "ネットワークエラー、後でもう一度お試しください",
 "j-dingtalk-web_pages_my-favorites_components_TrendingView_NoFavoriteProducts": "お気に入りの商品がありません",
 "j-dingtalk-web_pages_my-favorites_components_TrendingView_NetworkErrorPleaseTryAgain": "ネットワークエラー、後でもう一度お試しください",
 "j-dingtalk-web_pages_my-favorites_MyCollection": "私のお気に入り",
 "j-dingtalk-web_pages_my-favorites_Video": "動画",
 "j-dingtalk-web_pages_my-favorites_Shop": "ショップ",
 "j-dingtalk-web_pages_my-favorites_Commodity": "商品",
 "j-dingtalk-web_pages_my-history_components_ProductView_NoBrowsingHistory": "閲覧履歴がありません",
 "j-dingtalk-web_pages_my-history_components_ProductView_NetworkErrorPleaseTryAgain": "ネットワークエラー、後でもう一度お試しください",
 "j-dingtalk-web_pages_my-history_BrowsingHistory": "閲覧履歴",
 "j-dingtalk-web_pages_my-history_Video": "動画",
 "j-dingtalk-web_pages_my-history_Commodity": "商品",
 "j-dingtalk-web_pages_my-like_components_ProductCard_FailedToParseProductPayload": "商品ペイロードの解析に失敗しました:",
 "j-dingtalk-web_pages_my-like_ILikeIt": "いいねした商品",
 "j-dingtalk-web_pages_my-like_IDonTLikeThe": "いいねした商品がありません",
 "j-dingtalk-web_pages_my-like_NetworkErrorPleaseTryAgain": "ネットワークエラー、後でもう一度お試しください",
 "j-dingtalk-web_pages_work-center_Workbench": "ワークベンチ",
 "j-dingtalk-web_pages_work-center_ECommerceApplications": "Eコマースアプリケーション",
 "j-dingtalk-web_pages_work-center_ECommerceRelatedApplicationTools": "Eコマース関連のアプリケーションツール",
 "j-dingtalk-web_pages_work-center_CommoditySalesStatistics": "商品売上統計表",
 "j-dingtalk-web_pages_work-center_InventoryManagement": "在庫管理",
 "j-dingtalk-web_pages_work-center_UserEvaluationAnalysisTable": "ユーザー評価分析表",
 "j-dingtalk-web_pages_work-center_SupplyChainManagement": "サプライチェーン管理",
 "j-dingtalk-web_pages_work-center_GeneralApplication": "汎用アプリケーション",
 "j-dingtalk-web_pages_work-center_GeneralOfficeApplications": "汎用オフィスアプリケーション",
 "j-dingtalk-web_pages_work-center_AiTable": "AIテーブル",
 "j-dingtalk-web_pages_work-center_Document": "ドキュメント",
 "j-dingtalk-web_pages_work-center_Schedule": "スケジュール",
 "j-dingtalk-web_pages_work-center_Meeting": "会議",
 "j-dingtalk-web_pages_work-center_FailedToOpenTheLink": "リンクを開くのに失敗しました:",
  "j-dingtalk-web_pages_topMerchant_Sold": "販売数",
  "j-dingtalk-web_pages_topMerchant_Pieces": "件",
  "j-dingtalk-web_pages_work-center_AiMaterial": "AI素材の設計",
  "j-dingtalk-web_pages_work-center_EvaluationAndAnalysis": "評価分析",
};
