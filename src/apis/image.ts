import request from './base';

// 抠图服务
export const extractImage = (data: {} = {}) => {
  return request('/r/Adaptor/MaterialRpcI/extractImage', [data]);
};

// 生成图片
export const generateImage = (data: {} = {}) => {
  return request('/r/Adaptor/MaterialRpcI/generateImage', [data]);
};

// 查看图片生成状态
export const checkImageStatus = (data: {} = {}) => {
  return request('/r/Adaptor/MaterialRpcI/checkImageStatus', [data]);
};

// 生成高清大图
export const generateHDImage = (data: {} = {}) => {
  return request('/r/Adaptor/MaterialRpcI/generateHDImage', [data]);
};

// 获取图片列表
export const listImages = (data: {} = {}) => {
  return request('/r/Adaptor/MaterialRpcI/listImages', [data]);
};

// 删除图片
export const deleteImage = (data: {} = {}) => {
  return request('/r/Adaptor/MaterialRpcI/deleteImage', [data]);
};

// 重新生成图片
export const reGenerateImage = (data: {} = {}) => {
  return request('/r/Adaptor/MaterialRpcI/reGenerateImage', [data]);
};

// 评价图片
export const rateImage = (data: {} = {}) => {
  return request('/r/Adaptor/MaterialRpcI/rateImage', [data]);
};
