import request from './base';

// ==================== 类型定义 ====================

/**
 * 添加/取消收藏 FeedStore 请求
 */
export interface IRecFeedStoreFavoriteRequest {
  /**
   * 租户ID
   */
  tenantId: string;
  /**
   * Feed创建者ID
   */
  feedCreatorId: string;
}

/**
 * 添加/取消收藏 FeedStore 响应
 */
export interface IRecFeedStoreFavoriteResponse {
  success: boolean;
}

/**
 * 获取用户收藏的 FeedStore 列表请求
 */
export interface IRecFavoriteFeedStoreListRequest {
  /**
   * 租户ID
   */
  tenantId: string;
}

/**
 * FeedStore 信息
 */
export interface IRecFeedStore {
  /**
   * 创建者
   */
  creatorId: string;
  /**
   * 名称
   */
  name: string;
  /**
   * 头像
   */
  avatar: string;
  /**
   * feed个数
   */
  feedCount: number;
  /**
   * 收藏人数
   */
  followerCount: number;
}

/**
 * 获取用户收藏的 FeedStore 列表响应
 */
export interface IRecFavoriteFeedStoreListResponse {
  /**
   * FeedStore列表
   */
  stores: IRecFeedStore[];
  /**
   * 下一页游标
   */
  nextCursor: string;
  /**
   * 是否还有更多
   */
  hasMore: boolean;
}

/**
 * 用户商品请求
 */
export interface IJUserProductRequest {
  /**
   * 下一页游标
   */
  cursor: string;
  /**
   * 每页数量
   */
  size: number;

  hhoToken: string;
}

/**
 * 商品模型
 */
export interface IProjectModel {
  /**
   * 商品 ID
   */
  productId: string;
  /**
   * 商品标题
   */
  title: string;
  /**
   * 商品主图
   */
  primaryImage: string;
  /**
   * 商品价格
   */
  price: number;
  /**
   * 日期
   */
  date: string;

  productUrl?: string;
}

/**
 * 用户商品响应
 */
export interface IJUserProductResponse {
  /**
   * 商品列表
   */
  products: IProjectModel[];
  /**
   * 下一页游标
   */
  nextCursor: string;
  /**
   * 是否还有更多
   */
  hasMore: boolean;
}

/**
 * 视频Feed统计请求
 */
export interface IVideoFeedsCountByCreatorIdLwpRequest {
  /**
   * Feed创建者
   */
  creatorId: string;
}

/**
 * 视频Feed统计响应
 */
export interface IVideoFeedsCountByCreatorIdLwpResponse {
  /**
   * Feed总发布数
   */
  totalPostsCount: number;
  /**
   * Feed总点赞数
   */
  totalLikesCount: number;
  /**
   * FeedStore总收藏人数
   */
  totalFavoriteCount: number;
}

// ==================== API 接口 ====================

/**
 * 添加收藏 FeedStore
 */
export const addFavoriteFeedStore = (params: IRecFeedStoreFavoriteRequest): Promise<IRecFeedStoreFavoriteResponse> => {
  return request('/r/Adaptor/StoreRpcI/addFavoriteFeedStore', [params]);
};

/**
 * 取消收藏 FeedStore
 */
export const removeFavoriteFeedStore = (params: IRecFeedStoreFavoriteRequest): Promise<IRecFeedStoreFavoriteResponse> => {
  return request('/r/Adaptor/StoreRpcI/removeFavoriteFeedStore', [params]);
};

/**
 * 获取用户收藏的 FeedStore 列表
 */
export const queryUserFavoriteFeedStoreList = (params: IRecFavoriteFeedStoreListRequest): Promise<IRecFavoriteFeedStoreListResponse> => {
  return request('/r/Adaptor/StoreRpcI/queryUserFavoriteFeedStoreList', [params]);
};

/**
 * 获取用户收藏的商品
 */
export const queryUserFavoriteProductList = (params: IJUserProductRequest): Promise<IJUserProductResponse> => {
  return request('/r/Adaptor/ProductRpcI/queryUserFavoriteProductList', [params]);
};

/**
 * 获取用户历史浏览商品记录
 */
export const queryUserFootprint = (params: IJUserProductRequest): Promise<IJUserProductResponse> => {
  return request('/r/Adaptor/ProductRpcI/queryUserFootprint', [params]);
};

/**
 * 拉取店铺内的Feed流数量
 */
export const fetchFeedListCountByCreatorId = (params: IVideoFeedsCountByCreatorIdLwpRequest): Promise<IVideoFeedsCountByCreatorIdLwpResponse> => {
  return request('/r/Adaptor/UserRecommendI/fetchFeedListCountByCreatorId', [params]);
};
