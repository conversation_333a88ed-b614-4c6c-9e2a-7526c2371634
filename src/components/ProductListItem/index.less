@import (reference) "~dingtalk-theme/dingtalk-x/mob.less";

.product-list-item {
  display: flex;
  align-items: center;
  background: @common_bg_z1_color;
  border-radius: @common_border_radius_l;
  padding: 8px;
  cursor: pointer;
}

.product-list-item-img {
  width: 100px;
  height: 100px;
  object-fit: cover;
  border-radius: @common_border_radius_m;
  margin-right: 16px;
  background: @common_bg_color;
}

.product-list-item-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  min-height: 100px;
  overflow: hidden;
}

.product-list-item-title {
  .common_body_text_style_mob();
  color: @common_level1_base_color;
  margin: 8px 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.4;
  flex: 1;
}

.product-list-item-bottom {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  .product-list-item-price {
    .common_supertitle_text_style_mob();
    color: #FF0E53;
    font-weight: bold;
    line-height: 1;
    flex: 1
  }
  .product-list-item-like-icon {
    width: 13px;
    height: 13px;
  }

}





.product-list-item-like {
  display: flex;
  align-items: flex-end;
  margin-left: 12px;
}

.product-list-item-icon-heart {
  color: @theme_danger1_color;
  font-size: 24px;
  user-select: none;
}
