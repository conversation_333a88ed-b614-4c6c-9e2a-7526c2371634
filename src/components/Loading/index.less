.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  min-height: 100vh;
  background-color: var(--common_overlay_normal_color);
  position: relative;
  &.inline {
    min-height: unset;
    height: unset;
    width: unset;
    background-color: transparent;
    position: unset;

    .loading-content {
      width: auto;
      height: auto;
      background-color: transparent;
      box-shadow: none;
      border-radius: 0;
    }
  }
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: var(--popover_bg_color);
  width: 120px;
  height: 120px;
  border-radius: 12px;
  box-shadow: 0px 8px 24px 0px rgba(0, 0, 0, 0.16);
}

.loading-dots {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 30px;
}

.dot {
  width: 10px;
  height: 10px;
  background-color: #E1E1E1;
  transition: all 0.3s ease;
  transform: skew(-8deg);
  border-radius: 1px;
}

.dot-1 {
  animation: movingDot 1.02s infinite ease-in-out;
}

.dot-2 {
  animation: movingDot 1.02s infinite ease-in-out;
  animation-delay: 0.17s;
}

.dot-3 {
  animation: movingDot 1.02s infinite ease-in-out;
  animation-delay: 0.5s;
}

.loading-text {
  color: var(--common_white1_color);
  font-size: 14px;
  font-weight: normal;
  line-height: 22px;
  text-align: center;
}

@keyframes movingDot {
  0% {
    background-color: #FF0E53;
    transform: skew(-8deg) scale(1.1);
  }
  8% {
    background-color: #FF0E53;
    transform: skew(-8deg) scale(1.1);
  }
  16% {
    background-color: #FF4A7A;
    transform: skew(-8deg) scale(1.05);
  }
  24% {
    background-color: #FF8BA8;
    transform: skew(-8deg) scale(1);
  }
  32% {
    background-color: #E1E1E1;
    transform: skew(-8deg) scale(1);
  }
  100% {
    background-color: #E1E1E1;
    transform: skew(-8deg) scale(1);
  }
}
