import { i18next } from '@ali/dingtalk-i18n';
import React, { useEffect, useRef } from 'react';
import { useThrottleFn } from 'ahooks';
import { AppLOutlined, ErrorLOutlined } from '@ali/ding-icons';
import Loading from '@/components/Loading';
import './index.less';

export type InfiniteListLayout = 'waterfall' | 'list' | 'grid';

interface InfiniteListProps<T> {
  data: T[];
  renderItem: (item: T, index: number) => React.ReactNode;
  loading?: boolean;
  error?: boolean;
  hasMore?: boolean;
  onLoadMore?: () => void;
  layout?: InfiniteListLayout;
  columnCount?: number; // 仅 waterfall/grid 有效
  emptyText?: string;
  emptyIcon?: React.ReactNode;
  errorText?: string;
  errorIcon?: React.ReactNode;
  noMoreText?: string;
  className?: string;
  style?: React.CSSProperties;
}

function getDefaultNoMoreText() {
  return <span>·</span>;
}

function getDefaultEmptyText() {
  return i18next.t('j-dingtalk-web_components_InfiniteList_NoDataAvailable');
}
function getDefaultErrorText() {
  return i18next.t('j-dingtalk-web_components_InfiniteList_NetworkErrorPleaseTryAgain');
}

function splitColumns<T>(data: T[], columnCount: number): T[][] {
  const cols: T[][] = Array.from({ length: columnCount }, () => []);
  data.forEach((item, idx) => {
    cols[idx % columnCount].push(item);
  });
  return cols;
}

export function InfiniteList<T>({
  data,
  renderItem,
  loading,
  error,
  hasMore,
  onLoadMore,
  layout = 'list',
  columnCount = 2,
  emptyText,
  emptyIcon,
  errorText,
  errorIcon,
  noMoreText,
  className = '',
  style,
}: InfiniteListProps<T>) {
  const scrollRef = useRef<HTMLDivElement>(null);

  // 使用 ahook 的节流函数
  const { run: throttledScrollHandler } = useThrottleFn(
    () => {
      if (!scrollRef.current) return;

      const { scrollTop, scrollHeight, clientHeight } = scrollRef.current;
      if (scrollTop + clientHeight >= scrollHeight - 100 && !loading && hasMore) {
        onLoadMore();
      }
    },
    { wait: 1000, leading: true },
  );

  // 滚动触底加载
  useEffect(() => {
    if (!onLoadMore || loading || !hasMore) return;

    const handleScroll = () => {
      throttledScrollHandler();
    };

    if (scrollRef.current) {
      scrollRef.current.addEventListener('scroll', handleScroll);
    }
    return () => {
      if (scrollRef.current) {
        scrollRef.current.removeEventListener('scroll', handleScroll);
      }
    };
  }, [onLoadMore, loading, hasMore, throttledScrollHandler]);

  // 错误页
  if (error) {
    return (
      <div className={`infinite-list-page ${className}`} style={style}>
        <div className="infinite-list-content">
          <div className="infinite-list-empty">
            <span className="empty-icon">
              {errorIcon || <ErrorLOutlined />}
            </span>
            <div className="empty-text">{errorText || getDefaultErrorText()}</div>
          </div>
        </div>
      </div>
    );
  }

  // 空状态
  if (data.length === 0 && !loading) {
    return (
      <div className={`infinite-list-page ${className}`} style={style}>
        <div className="infinite-list-content">
          <div className="infinite-list-empty">
            <span className="empty-icon">
              {emptyIcon || <AppLOutlined />}
            </span>
            <div className="empty-text">{emptyText || getDefaultEmptyText()}</div>
          </div>
        </div>
      </div>
    );
  }

  // 列表布局
  let listBody: React.ReactNode = null;
  if (layout === 'waterfall' || layout === 'grid') {
    const columns = splitColumns(data, columnCount);
    listBody = (
      <div className={`infinite-list-${layout}-container`}>
        {columns.map((col, idx) => (
          <div key={idx} className={`infinite-list-${layout}-column`}>
            {col.map((item, i) => renderItem(item, i))}
          </div>
        ))}
      </div>
    );
  } else {
    // 普通列表
    listBody =
      (<div className="infinite-list-list-container">
        {data.map((item, idx) => renderItem(item, idx))}
      </div>);
  }

  return (
    <div className={`infinite-list-page ${className}`} style={style} ref={scrollRef}>
      <div className="infinite-list-content">
        {listBody}
        {/* 加载中 */}
        {loading &&
          <Loading text="" className="infinite-list-loading" inline />
        }
        {/* 没有更多数据 */}
        {!hasMore && data.length > 0 &&
          <div className="infinite-list-no-more">
            {noMoreText || getDefaultNoMoreText()}
          </div>
        }
      </div>
    </div>);
}

export default InfiniteList;
